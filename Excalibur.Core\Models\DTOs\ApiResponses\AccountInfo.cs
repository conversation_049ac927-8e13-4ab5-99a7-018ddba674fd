﻿using System.Text.Json.Serialization;

namespace Excalibur.Core.Models.DTOs.ApiResponses;

public class AccountInfo
{
    [JsonPropertyName("loginid")]
    public string LoginId { get; set; } = string.Empty;

    [JsonPropertyName("balance")]
    public decimal Balance { get; set; }

    [JsonPropertyName("currency")]
    public string Currency { get; set; } = string.Empty;

    [JsonPropertyName("is_virtual")]
    public int IsVirtual { get; set; }

    [JsonPropertyName("email")]
    public string Email { get; set; } = string.Empty;

    [JsonPropertyName("fullname")]
    public string FullName { get; set; } = string.Empty;

    [JsonPropertyName("country")]
    public string Country { get; set; } = string.Empty;

    [JsonPropertyName("landing_company_name")]
    public string LandingCompanyName { get; set; } = string.Empty;

    [JsonPropertyName("landing_company_fullname")]
    public string LandingCompanyFullName { get; set; } = string.Empty;

    [JsonPropertyName("preferred_language")]
    public string PreferredLanguage { get; set; } = string.Empty;

    [JsonPropertyName("user_id")]
    public long UserId { get; set; }

    [JsonPropertyName("account_list")]
    public List<AccountListItem> AccountList { get; set; } = new();

    [JsonPropertyName("scopes")]
    public List<string> Scopes { get; set; } = new();

    [JsonPropertyName("upgradeable_landing_companies")]
    public List<string> UpgradeableLandingCompanies { get; set; } = new();

    [JsonPropertyName("linked_to")]
    public List<object> LinkedTo { get; set; } = new();

    [JsonPropertyName("local_currencies")]
    public Dictionary<string, object> LocalCurrencies { get; set; } = new();
}

public class AccountListItem
{
    [JsonPropertyName("loginid")]
    public string LoginId { get; set; } = string.Empty;

    [JsonPropertyName("account_category")]
    public string AccountCategory { get; set; } = string.Empty;

    [JsonPropertyName("account_type")]
    public string AccountType { get; set; } = string.Empty;

    [JsonPropertyName("broker")]
    public string Broker { get; set; } = string.Empty;

    [JsonPropertyName("currency")]
    public string Currency { get; set; } = string.Empty;

    [JsonPropertyName("currency_type")]
    public string CurrencyType { get; set; } = string.Empty;

    [JsonPropertyName("is_disabled")]
    public int IsDisabled { get; set; }

    [JsonPropertyName("is_virtual")]
    public int IsVirtual { get; set; }

    [JsonPropertyName("landing_company_name")]
    public string LandingCompanyName { get; set; } = string.Empty;

    [JsonPropertyName("created_at")]
    public long CreatedAt { get; set; }

    [JsonPropertyName("linked_to")]
    public List<object> LinkedTo { get; set; } = new();
}