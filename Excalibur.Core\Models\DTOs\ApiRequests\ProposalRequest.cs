using System.Text.Json.Serialization;

namespace Excalibur.Core.Models.DTOs.ApiRequests;

public class ProposalRequest
{
    [JsonPropertyName("proposal")]
    public int Proposal { get; set; } = 1;

    [JsonPropertyName("amount")]
    public decimal Amount { get; set; }

    [JsonPropertyName("basis")]
    public string Basis { get; set; } = "stake";

    [JsonPropertyName("contract_type")]
    public string ContractType { get; set; } = string.Empty;

    [JsonPropertyName("currency")]
    public string Currency { get; set; } = "USD";

    [JsonPropertyName("duration")]
    public int Duration { get; set; }

    [JsonPropertyName("duration_unit")]
    public string DurationUnit { get; set; } = "t";

    [JsonPropertyName("symbol")]
    public string Symbol { get; set; } = string.Empty;

    [JsonPropertyName("barrier")]
    public string? Barrier { get; set; }

    [JsonPropertyName("barrier2")]
    public string? Barrier2 { get; set; }

    [JsonPropertyName("prediction")]
    public int? Prediction { get; set; }

    [JsonPropertyName("req_id")]
    public int ReqId { get; set; }
}