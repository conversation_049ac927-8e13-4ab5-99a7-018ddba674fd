﻿using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace Excalibur.Converters;

public class AccountTypeToColorConverter : IValueConverter
{
    public object Convert (object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is string accountType)
        {
            return accountType.ToLower() switch {
                "real" => new SolidColorBrush(Colors.Green),
                "virtual" => new SolidColorBrush(Colors.Orange),
                _ => new SolidColorBrush(Colors.Gray)
            };
        }

        return new SolidColorBrush(Colors.Gray);
    }

    public object ConvertBack (object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}