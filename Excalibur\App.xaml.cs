﻿using System.IO;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Excalibur.Core.Interfaces;
using Excalibur.Core.Services;
using Excalibur.ViewModels;
using Excalibur.Services;
using System.Windows.Threading;

namespace Excalibur;

public partial class App : Application
{
    private IHost? _host;

    protected override void OnStartup(StartupEventArgs e)
    {
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // Registrar serviços
                services.AddSingleton<IDerivApiService, DerivApiService>();

                // Registrar ViewModels
                services.AddTransient<MainViewModel>();
                services.AddSingleton<AccountInfoViewModel>();
                services.AddSingleton<LogViewModel>();
                services.AddSingleton<ActiveSymbolsViewModel>();
                services.AddSingleton<ProposalViewModel>();

                // Logging provider
                services.AddLogging(builder =>
                {
                    builder.AddDebug();
                    builder.SetMinimumLevel(LogLevel.Debug);
                });

                // Registrar Views
                services.AddTransient<MainWindow>();
            })
            .Build();

        base.OnStartup(e);

        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        
        // Obter os loggers necessários
        var purchaseLogger = _host.Services.GetRequiredService<ILogger<PurchaseViewModel>>();
        var martingaleLogger = _host.Services.GetRequiredService<ILogger<MartingaleService>>();
        
        // Criar MainViewModel manualmente com os loggers
        var derivApiService = _host.Services.GetRequiredService<IDerivApiService>();
        var accountInfoViewModel = _host.Services.GetRequiredService<AccountInfoViewModel>();
        var activeSymbolsViewModel = _host.Services.GetRequiredService<ActiveSymbolsViewModel>();
        var proposalViewModel = _host.Services.GetRequiredService<ProposalViewModel>();
        
        var mainViewModel = new MainViewModel(derivApiService, accountInfoViewModel, activeSymbolsViewModel, proposalViewModel, purchaseLogger, martingaleLogger);

        mainWindow.DataContext = mainViewModel;
        mainWindow.Show();

        // Reset log file on startup
        var logFilePath = "log.txt";
        if (File.Exists(logFilePath))
        {
            File.Delete(logFilePath);
        }

        // Add file logger
        var loggerFactory = _host.Services.GetRequiredService<ILoggerFactory>();
        loggerFactory.AddProvider(new Excalibur.Infrastructure.FileLoggerProvider(logFilePath));
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _host?.Dispose();
        base.OnExit(e);
    }

    public App()
    {
        DispatcherUnhandledException += (s, args) =>
        {
            MessageBox.Show(args.Exception.Message, "Unhandled UI Exception", MessageBoxButton.OK, MessageBoxImage.Error);
            args.Handled = true;
        };

        AppDomain.CurrentDomain.UnhandledException += (s, args) =>
        {
            if (args.ExceptionObject is Exception ex)
            {
                MessageBox.Show(ex.Message, "Unhandled Non-UI Exception", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        };
    }
}