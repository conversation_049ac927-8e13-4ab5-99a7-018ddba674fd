using Microsoft.Extensions.Logging;
using Excalibur.ViewModels;

namespace Excalibur.Infrastructure;

public class LogObservableProvider : ILoggerProvider
{
    private readonly LogViewModel _vm;
    public LogObservableProvider(LogViewModel vm) => _vm = vm;

    public ILogger CreateLogger(string categoryName) => new Logger(categoryName, _vm);

    public void Dispose() {}

    private class Logger : ILogger
    {
        private readonly string _category;
        private readonly LogViewModel _vm;
        public Logger(string cat, LogViewModel vm){_category=cat;_vm=vm;}
        public IDisposable? BeginScope<TState>(TState state) where TState : notnull => null;
        public bool IsEnabled(LogLevel logLevel)=>true;
        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
        {
            var msg=$"[{logLevel}] {_category}: {formatter(state, exception)}";
            _vm.Add(msg);
        }
    }
} 