<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="Excalibur.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <!-- Proposal Settings -->
    <Setting Name="ProposalStakeText" Type="System.String" Scope="User">
      <Value Profile="(Default)">0.35</Value>
    </Setting>
    <Setting Name="ProposalDuration" Type="System.String" Scope="User">
      <Value Profile="(Default)">5</Value>
    </Setting>
    <Setting Name="ProposalDurationType" Type="System.String" Scope="User">
      <Value Profile="(Default)">t</Value>
    </Setting>
    <Setting Name="ProposalBarrierText" Type="System.String" Scope="User">
      <Value Profile="(Default)"></Value>
    </Setting>
    <Setting Name="ProposalIsContinuous" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="ProposalSaveConfiguration" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    
    <!-- Money Management Settings -->
    <Setting Name="MoneyManagementFactorText" Type="System.String" Scope="User">
      <Value Profile="(Default)">2</Value>
    </Setting>
    <Setting Name="MoneyManagementLevelText" Type="System.String" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="MoneyManagementMartingaleEnabled" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="MoneyManagementAmostraText" Type="System.String" Scope="User">
      <Value Profile="(Default)">5</Value>
    </Setting>
    <Setting Name="MoneyManagementWinText" Type="System.String" Scope="User">
      <Value Profile="(Default)">2</Value>
    </Setting>
    <Setting Name="MoneyManagementLossText" Type="System.String" Scope="User">
      <Value Profile="(Default)">3</Value>
    </Setting>
    <Setting Name="MoneyManagementSaveConfiguration" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
  </Settings>
</SettingsFile> 