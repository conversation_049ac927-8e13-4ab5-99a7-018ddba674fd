using System.Text.Json.Serialization;

namespace Excalibur.Core.Models.DTOs.ApiResponses;

public class ContractType
{
    [JsonPropertyName("contract_type")]
    public string ContractTypeValue { get; set; } = string.Empty;

    [JsonPropertyName("contract_display")]
    public string ContractDisplay { get; set; } = string.Empty;

    [JsonPropertyName("contract_category")]
    public string ContractCategory { get; set; } = string.Empty;

    [JsonPropertyName("contract_category_display")]
    public string ContractCategoryDisplay { get; set; } = string.Empty;

    [JsonPropertyName("sentiment")]
    public string Sentiment { get; set; } = string.Empty;

    [JsonPropertyName("expiry_type")]
    public string ExpiryType { get; set; } = string.Empty;

    [JsonPropertyName("min_contract_duration")]
    public string MinContractDuration { get; set; } = string.Empty;

    [JsonPropertyName("max_contract_duration")]
    public string MaxContractDuration { get; set; } = string.Empty;

    [JsonPropertyName("barrier_category")]
    public string BarrierCategory { get; set; } = string.Empty;

    [JsonPropertyName("barriers")]
    public int Barriers { get; set; }

    [JsonPropertyName("submarket")]
    public string Submarket { get; set; } = string.Empty;

    [JsonPropertyName("underlying_symbol")]
    public string UnderlyingSymbol { get; set; } = string.Empty;

    [JsonPropertyName("high_barrier")]
    public string? HighBarrier { get; set; }

    [JsonPropertyName("low_barrier")]
    public string? LowBarrier { get; set; }

    [JsonPropertyName("barrier")]
    public string? Barrier { get; set; }

    [JsonPropertyName("barrier2")]
    public string? Barrier2 { get; set; }

    [JsonPropertyName("barrier_choices")]
    public List<string>? BarrierChoices { get; set; }
}