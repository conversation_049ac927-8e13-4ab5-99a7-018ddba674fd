﻿<Window x:Class="Excalibur.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:views="clr-namespace:Excalibur.Views"
        Title="Excalibur - Trading Platform"
        Height="720"
        Width="1280"
        MinHeight="600"
        MinWidth="800"
        WindowStartupLocation="CenterScreen"
        WindowState="Normal"
        Background="#FF1E1E1E"
        WindowStyle="SingleBorderWindow"
        ResizeMode="CanResize">

    <Grid>
        <!-- Card da Conta - Canto Superior Esquerdo -->
        <views:AccountInfoView DataContext="{Binding AccountInfo}"
                               HorizontalAlignment="Left"
                               VerticalAlignment="Top"
                               Margin="20,20,0,0"
                               Panel.ZIndex="10"/>

        <!-- Card dos Contratos abaixo do AccountInfo -->
        <Border Background="#FF2D2D30"
                BorderBrush="#FF404040"
                BorderThickness="1"
                CornerRadius="12"
                Width="320"
                HorizontalAlignment="Left"
                VerticalAlignment="Stretch"
                Margin="20,177,0,20"
                Panel.ZIndex="11">
            <Border.Effect>
                <DropShadowEffect Color="Black"
                                   Opacity="0.3"
                                   BlurRadius="15"
                                   ShadowDepth="5"/>
            </Border.Effect>

            <views:ContractsView DataContext="{Binding Contracts}" />
        </Border>

        <!-- Card Proposta -->
        <Border Background="#FF2D2D30"
                BorderBrush="#FF404040"
                BorderThickness="1"
                CornerRadius="12"
                Width="320"
                Height="390"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Margin="346,20,0,0"
                Panel.ZIndex="11">
            <Border.Effect>
                <DropShadowEffect Color="Black"
                                   Opacity="0.3"
                                   BlurRadius="15"
                                   ShadowDepth="5"/>
            </Border.Effect>

            <views:ProposalView DataContext="{Binding Proposal}" />
        </Border>

        <!-- Card Money Management abaixo da Proposta -->
        <Border Background="#FF2D2D30"
                BorderBrush="#FF404040"
                BorderThickness="1"
                CornerRadius="12"
                Width="320"
                HorizontalAlignment="Left"
                VerticalAlignment="Stretch"
                Margin="346,417,0,20"
                Panel.ZIndex="11">
            <Border.Effect>
                <DropShadowEffect Color="Black"
                                   Opacity="0.3"
                                   BlurRadius="15"
                                   ShadowDepth="5"/>
            </Border.Effect>

            <views:MoneyManagementView DataContext="{Binding MoneyManagement}" />
        </Border>

        <!-- Card Simulation -->
        <Border Background="#FF2D2D30"
                BorderBrush="#FF404040"
                BorderThickness="1"
                CornerRadius="12"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Stretch"
                Margin="672,20,20,350"
                Panel.ZIndex="10">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.3" BlurRadius="15" ShadowDepth="5"/>
            </Border.Effect>

            <views:SimulationView DataContext="{Binding Simulation}" />
        </Border>

        <!-- Card Purchase abaixo do Simulation -->
        <Border Background="#FF2D2D30"
                BorderBrush="#FF404040"
                BorderThickness="1"
                CornerRadius="12"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Stretch"
                Margin="672,339,20,20"
                Panel.ZIndex="10">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.3" BlurRadius="15" ShadowDepth="5"/>
            </Border.Effect>

            <views:PurchaseView DataContext="{Binding Purchase}"/>
        </Border>
    </Grid>
</Window>