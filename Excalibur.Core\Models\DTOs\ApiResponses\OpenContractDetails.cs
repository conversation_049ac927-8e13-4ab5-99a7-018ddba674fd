using System.Text.Json.Serialization;

namespace Excalibur.Core.Models.DTOs.ApiResponses;

public class OpenContractDetails
{
    [JsonPropertyName("contract_id")]
    public long ContractId { get; set; }

    [JsonPropertyName("transaction_id")]
    public long TransactionId { get; set; }

    [JsonPropertyName("profit")]
    public decimal Profit { get; set; }

    [JsonPropertyName("payout")]
    public decimal Payout { get; set; }

    [JsonPropertyName("bid_price")]
    public decimal BidPrice { get; set; }

    [JsonPropertyName("buy_price")]
    public decimal BuyPrice { get; set; }

    [JsonPropertyName("sell_price")]
    public decimal SellPrice { get; set; }

    [JsonPropertyName("current_spot")]
    public decimal CurrentSpot { get; set; }

    [JsonPropertyName("current_spot_time")]
    public long CurrentSpotTime { get; set; }

    [JsonPropertyName("date_start")]
    public long DateStart { get; set; }

    [JsonPropertyName("date_expiry")]
    public long DateExpiry { get; set; }

    [JsonPropertyName("is_expired")]
    public bool IsExpired { get; set; }

    [JsonPropertyName("is_settleable")]
    public bool IsSettleable { get; set; }

    [JsonPropertyName("is_sold")]
    public bool IsSold { get; set; }

    [JsonPropertyName("is_valid_to_sell")]
    public bool IsValidToSell { get; set; }

    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty;

    [JsonPropertyName("contract_type")]
    public string ContractType { get; set; } = string.Empty;

    [JsonPropertyName("shortcode")]
    public string Shortcode { get; set; } = string.Empty;

    [JsonPropertyName("underlying")]
    public string Underlying { get; set; } = string.Empty;

    [JsonPropertyName("barrier")]
    public string Barrier { get; set; } = string.Empty;

    [JsonPropertyName("entry_spot")]
    public decimal EntrySpot { get; set; }

    [JsonPropertyName("exit_tick")]
    public decimal ExitTick { get; set; }

    [JsonPropertyName("exit_tick_time")]
    public long ExitTickTime { get; set; }

    // IDs como string para compatibilidade com o resto do código
    public string ContractIdString => ContractId.ToString();
    public string TransactionIdString => TransactionId.ToString();
} 