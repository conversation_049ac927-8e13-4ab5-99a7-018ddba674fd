using System.Text.Json.Serialization;

namespace Excalibur.Core.Models.DTOs.ApiResponses;

public class ProfitTableResponse
{
    [JsonPropertyName("profit_table")]
    public ProfitTableTransaction[]? ProfitTable { get; set; }
}

public class ProfitTableTransaction
{
    [JsonPropertyName("contract_id")]
    public long ContractId { get; set; }

    [JsonPropertyName("transaction_id")]
    public long TransactionId { get; set; }

    [JsonPropertyName("app_id")]
    public int AppId { get; set; }

    [JsonPropertyName("buy_price")]
    public decimal BuyPrice { get; set; }

    [JsonPropertyName("sell_price")]
    public decimal SellPrice { get; set; }

    [JsonPropertyName("payout")]
    public decimal Payout { get; set; }

    [JsonPropertyName("purchase_time")]
    public long PurchaseTime { get; set; }

    [JsonPropertyName("sell_time")]
    public long SellTime { get; set; }

    [JsonPropertyName("shortcode")]
    public string Shortcode { get; set; } = string.Empty;

    [JsonPropertyName("contract_type")]
    public string ContractType { get; set; } = string.Empty;

    [JsonPropertyName("duration")]
    public int Duration { get; set; }

    [JsonPropertyName("duration_type")]
    public string DurationType { get; set; } = string.Empty;

    // Propriedade calculada para compatibilidade
    public decimal Profit => SellPrice - BuyPrice;

    // IDs como string para compatibilidade com o resto do código
    public string ContractIdString => ContractId.ToString();
    public string TransactionIdString => TransactionId.ToString();
} 