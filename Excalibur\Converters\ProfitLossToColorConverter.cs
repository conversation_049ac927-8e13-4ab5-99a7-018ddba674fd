using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace Excalibur.Converters;

public class ProfitLossToColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is decimal profitLoss)
        {
            return profitLoss >= 0 ? 
                new SolidColorBrush(Color.FromRgb(76, 175, 80)) :  // Verde
                new SolidColorBrush(Color.FromRgb(244, 67, 54));   // Vermelho
        }
        
        if (value is string stringValue && decimal.TryParse(stringValue, out decimal parsedValue))
        {
            return parsedValue >= 0 ? 
                new SolidColorBrush(Color.FromRgb(76, 175, 80)) :  // Verde
                new SolidColorBrush(Color.FromRgb(244, 67, 54));   // Vermelho
        }

        return new SolidColorBrush(Color.FromRgb(204, 204, 204)); // Cinza padrão
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
} 