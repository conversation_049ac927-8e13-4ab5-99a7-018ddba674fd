<Window x:Class="Excalibur.Views.LogWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Log" Width="600" Height="300" Background="#FF1E1E1E">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Button Content="Copy All" Margin="5" Padding="5,2" HorizontalAlignment="Right" Click="CopyAll_Click"/>
        <TextBox Grid.Row="1" Text="{Binding All, Mode=OneWay}"
                 Background="#FF252526"
                 Foreground="#FFCCCCCC"
                 FontFamily="Consolas"
                 IsReadOnly="True"
                 TextWrapping="NoWrap"
                 VerticalScrollBarVisibility="Auto"
                 HorizontalScrollBarVisibility="Auto"
                 AcceptsReturn="True"
                 AcceptsTab="True" />
    </Grid>
</Window> 