//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Excalibur.Properties {
    
    
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "17.0.0.0")]
    internal sealed partial class Settings : global::System.Configuration.ApplicationSettingsBase {
        
        private static Settings defaultInstance = ((Settings)(global::System.Configuration.ApplicationSettingsBase.Synchronized(new Settings())));
        
        public static Settings Default {
            get {
                return defaultInstance;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("0.35")]
        public string ProposalStakeText {
            get {
                return ((string)(this["ProposalStakeText"]));
            }
            set {
                this["ProposalStakeText"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("5")]
        public string ProposalDuration {
            get {
                return ((string)(this["ProposalDuration"]));
            }
            set {
                this["ProposalDuration"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("t")]
        public string ProposalDurationType {
            get {
                return ((string)(this["ProposalDurationType"]));
            }
            set {
                this["ProposalDurationType"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string ProposalBarrierText {
            get {
                return ((string)(this["ProposalBarrierText"]));
            }
            set {
                this["ProposalBarrierText"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool ProposalIsContinuous {
            get {
                return ((bool)(this["ProposalIsContinuous"]));
            }
            set {
                this["ProposalIsContinuous"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool ProposalSaveConfiguration {
            get {
                return ((bool)(this["ProposalSaveConfiguration"]));
            }
            set {
                this["ProposalSaveConfiguration"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("2")]
        public string MoneyManagementFactorText {
            get {
                return ((string)(this["MoneyManagementFactorText"]));
            }
            set {
                this["MoneyManagementFactorText"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("1")]
        public string MoneyManagementLevelText {
            get {
                return ((string)(this["MoneyManagementLevelText"]));
            }
            set {
                this["MoneyManagementLevelText"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool MoneyManagementMartingaleEnabled {
            get {
                return ((bool)(this["MoneyManagementMartingaleEnabled"]));
            }
            set {
                this["MoneyManagementMartingaleEnabled"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("5")]
        public string MoneyManagementAmostraText {
            get {
                return ((string)(this["MoneyManagementAmostraText"]));
            }
            set {
                this["MoneyManagementAmostraText"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("2")]
        public string MoneyManagementWinText {
            get {
                return ((string)(this["MoneyManagementWinText"]));
            }
            set {
                this["MoneyManagementWinText"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("3")]
        public string MoneyManagementLossText {
            get {
                return ((string)(this["MoneyManagementLossText"]));
            }
            set {
                this["MoneyManagementLossText"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool MoneyManagementSaveConfiguration {
            get {
                return ((bool)(this["MoneyManagementSaveConfiguration"]));
            }
            set {
                this["MoneyManagementSaveConfiguration"] = value;
            }
        }
    }
} 