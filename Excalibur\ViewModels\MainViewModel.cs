﻿using Excalibur.Core.Interfaces;
using Excalibur.Models;
using Excalibur.Core.Models.DTOs.ApiResponses;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Logging;
using Excalibur.Services;

namespace Excalibur.ViewModels;

public class MainViewModel : BaseViewModel, IDisposable
{
    private readonly IDerivApiService _derivApiService;
    private System.Timers.Timer? _profitTableTimer;
    private readonly SemaphoreSlim _timerSemaphore = new SemaphoreSlim(1, 1);
    private bool _disposed = false;
    
    public AccountInfoViewModel AccountInfo { get; }
    public ActiveSymbolsViewModel Contracts { get; }
    public ProposalViewModel Proposal { get; }
    public MoneyManagementViewModel MoneyManagement { get; }
    public SimulationViewModel Simulation { get; }
    public PurchaseViewModel Purchase { get; }

    public MainViewModel(IDerivApiService derivApiService, AccountInfoViewModel accountInfoViewModel, ActiveSymbolsViewModel activeSymbolsViewModel, ProposalViewModel proposalViewModel, ILogger<PurchaseViewModel> purchaseLogger, ILogger<MartingaleService> martingaleLogger)
    {
        _derivApiService = derivApiService;
        
        // O derivApiService é injetado mas não precisa ser usado diretamente aqui
        // A comunicação é feita através do AccountInfoViewModel
        AccountInfo = accountInfoViewModel;
        Contracts = activeSymbolsViewModel;
        Proposal = proposalViewModel;
        
        // Instanciar VM de Money Management (por enquanto sem dependências externas)
        MoneyManagement = new MoneyManagementViewModel();
        
        Simulation = new SimulationViewModel();
        
        // Criar MartingaleService com logger
        var martingaleService = new MartingaleService(martingaleLogger);
        
        Purchase = new PurchaseViewModel(purchaseLogger, MoneyManagement, martingaleService);
        
        // Configurar verificador de critérios de simulação no MartingaleService
        Purchase.ConfigureSimulationCriteriaChecker(Simulation.CheckSimulationCriteriaAsync);
        
        // Contrato expirado -> buscar resultado real via API
        Purchase.ContractExpired += OnContractExpired;
        
        // Conectar evento de reset do martingale
        Purchase.MartingaleReset += OnMartingaleReset;
        
        // Conectar eventos entre ViewModels
        Contracts.ContractSelected += OnContractSelected;
        
        // Conectar evento de compra do ProposalViewModel com o PurchaseViewModel
        Proposal.ContractPurchased += OnContractPurchased;
        
        // Conectar evento de atualização do ID do contrato
        Proposal.ContractIdUpdated += OnContractIdUpdated;
        
        // Conectar evento de simulação do ProposalViewModel com o SimulationViewModel
        System.Diagnostics.Debug.WriteLine($"[MAIN] Conectando evento ContractSimulated...");
        Proposal.ContractSimulated += OnContractSimulated;
        System.Diagnostics.Debug.WriteLine($"[MAIN] Evento ContractSimulated conectado com sucesso!");
        
        // Conectar evento de limpeza de tabelas
        Proposal.ClearTablesRequested += OnClearTablesRequested;
        
        // Conectar trigger para compra real quando simulação completar amostra
        Simulation.TriggerRealPurchase += OnTriggerRealPurchase;
        
        // Configurar simulação com valores iniciais do MoneyManagement
        System.Diagnostics.Debug.WriteLine($"[MAIN] Configurando simulação inicial: Amostra={MoneyManagement.Amostra}, Win={MoneyManagement.Win}, Loss={MoneyManagement.Loss}");
        Simulation.UpdateSampleConfiguration(MoneyManagement.Amostra, MoneyManagement.Win, MoneyManagement.Loss);
        
        // Conectar evento de tick para atualizar transações em tempo real
        _derivApiService.TickReceived += OnTickReceived;

        // Receber resultado da ProfitTable
        _derivApiService.ProfitTableReceived += OnProfitTableReceived;
        
        // Receber detalhes de contratos individuais
        _derivApiService.OpenContractReceived += OnOpenContractReceived;
        
        // Timer para buscar profit_table periodicamente (a cada 15 segundos para reduzir rate limiting)
        _profitTableTimer = new System.Timers.Timer(15000);
        _profitTableTimer.Elapsed += (sender, e) => 
        {
            // Use fire-and-forget with proper error handling
            _ = Task.Run(async () =>
            {
                if (_disposed) return;

                // Prevent overlapping executions
                if (!await _timerSemaphore.WaitAsync(100))
                {
                    System.Diagnostics.Debug.WriteLine("Timer execution skipped - previous execution still running");
                    return;
                }

                try
                {
                    await _derivApiService.RequestProfitTableAsync();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error requesting profit table: {ex.Message}");
                }
                finally
                {
                    _timerSemaphore.Release();
                }
            });
        };
        _profitTableTimer.AutoReset = true;
        _profitTableTimer.Start();
    }

    private void OnContractSelected(object? sender, (string Symbol, Excalibur.Core.Models.DTOs.ApiResponses.ContractType Contract) e)
    {
        Proposal.SelectedSymbol = e.Symbol;
        Proposal.SelectedContract = e.Contract;
    }
    
    private void OnContractPurchased(object? sender, ContractPurchasedEventArgs e)
    {
        if (_disposed) return;
        
        try
        {
            System.Diagnostics.Debug.WriteLine($"[MAIN] ⚡ EVENTO OnContractPurchased RECEBIDO!");
            System.Diagnostics.Debug.WriteLine($"[MAIN] ContractType: {e.ContractType}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] ContractId: {e.ContractId}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] StartSpot: {e.StartSpot}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] Stake: {e.Stake}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] Payout: {e.Payout}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] Duration: {e.Duration}{e.DurationType}");
            
            var transaction = new ContractTransaction
            {
                Type = e.ContractType,
                RefId = e.ContractId,
                BuyTime = DateTime.Now,
                StartSpot = e.StartSpot,
                EndSpot = e.StartSpot, // Inicialmente igual ao StartSpot
                Stake = e.Stake,
                Payout = e.Payout,
                TotalProfitLoss = 0, // Inicialmente zero
                IsActive = true,
                Duration = e.Duration,
                DurationType = e.DurationType
            };
            
            System.Diagnostics.Debug.WriteLine($"[MAIN] Criando transação: RefId={transaction.RefId}, BuyTime={transaction.BuyTime:HH:mm:ss}");
            
            Purchase.AddTransaction(transaction);
            
            System.Diagnostics.Debug.WriteLine($"[MAIN] ✅ Transação adicionada à tabela de compras!");
            System.Diagnostics.Debug.WriteLine($"[MAIN] Total de transações na tabela: {Purchase.PurchaseTransactions.Count}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[MAIN] ❌ Erro em OnContractPurchased: {ex.Message}");
        }
    }
    
    private void OnContractIdUpdated(object? sender, ContractIdUpdatedEventArgs e)
    {
        // Atualizar o ID do contrato na transação
        Purchase.UpdateContractId(e.OldProposalId, e.NewContractId);
    }
    
    private void OnTickReceived(object? sender, TickData tickData)
    {
        // Atualizar spot atual para cálculo do P/L em tempo real
        Purchase.UpdateCurrentSpot(tickData.Price);
        Simulation.UpdateCurrentSpot(tickData.Price);
    }

    private void OnContractExpired(object? sender, ContractTransaction transaction)
    {
        if (_disposed || transaction == null) return;

        var status = transaction.IsCurrentlyWinning(transaction.EndSpot) ? "Vencedor" : "Perdedor";
        System.Diagnostics.Debug.WriteLine($"CONTRATO EXPIRADO: RefId={transaction.RefId}, Tipo={transaction.Type}, StartSpot={transaction.StartSpot}, EndSpot={transaction.EndSpot}, Status={status}");
        
        // Remover chamada duplicada de RequestContractDetailsAsync - o timer já faz isso
        // Apenas solicitar a profit_table geral para atualizar todos os contratos
        _ = Task.Run(async () =>
        {
            try
            {
                if (_derivApiService?.IsConnected == true && !_disposed)
                {
                    await _derivApiService.RequestProfitTableAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling contract expiry: {ex.Message}");
            }
        });
    }

    private void OnMartingaleReset(object? sender, MartingaleResetEventArgs e)
    {
        if (_disposed) return;

        try
        {
            System.Diagnostics.Debug.WriteLine($"[MAIN] 🔔 Martingale resetado! Motivo: {e.ResetReason}, Stake original: {e.OriginalStake}");
            
            // Resetar stake no ProposalViewModel para o valor original
            Proposal.ResetStakeToOriginal(e.OriginalStake);
            
            System.Diagnostics.Debug.WriteLine($"[MAIN] ✅ Stake do ProposalViewModel resetado para: {e.OriginalStake}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[MAIN] ❌ Erro ao processar reset do martingale: {ex.Message}");
        }
    }

    private void OnContractSimulated(object? sender, ContractSimulatedEventArgs e)
    {
        if (_disposed || e == null) return;

        try
        {
            System.Diagnostics.Debug.WriteLine($"[MAIN] ======= EVENTO CONTRACTSIMULATED RECEBIDO =======");
            System.Diagnostics.Debug.WriteLine($"[MAIN] Sender: {sender?.GetType().Name}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] ContractType: {e.ContractType}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] ContractId: {e.ContractId}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] StartSpot: {e.StartSpot}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] Stake: {e.Stake}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] Payout: {e.Payout}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] Duration: {e.Duration} {e.DurationType}");

            // Atualizar configuração da simulação com valores atuais do MoneyManagement
            System.Diagnostics.Debug.WriteLine($"[MAIN] Atualizando configuração: Amostra={MoneyManagement.Amostra}, Win={MoneyManagement.Win}, Loss={MoneyManagement.Loss}");
            Simulation.UpdateSampleConfiguration(MoneyManagement.Amostra, MoneyManagement.Win, MoneyManagement.Loss);

            // Criar transação simulada usando a stake ORIGINAL do ProposalViewModel (não afetada pelo martingale)
            var actualStake = Proposal?.OriginalStake ?? e.Stake;
            
            // Usar o tempo atual como BuyTime para timing preciso
            var simulationBuyTime = DateTime.Now;
            
            var simulatedTransaction = new ContractTransaction
            {
                Type = e.ContractType,
                RefId = e.ContractId,
                BuyTime = simulationBuyTime, // Usar tempo atual para timing preciso
                StartSpot = e.StartSpot,
                EndSpot = Proposal.CurrentSpot, // Usar spot atual do ProposalViewModel
                Stake = actualStake, // Usar stake real do painel
                Payout = e.Payout,
                TotalProfitLoss = 0,
                IsActive = true,
                Duration = e.Duration,
                DurationType = e.DurationType
            };

            System.Diagnostics.Debug.WriteLine($"[MAIN] Usando stake ORIGINAL para simulação: {actualStake} (ProposalVM.Stake: {Proposal?.Stake}, ProposalVM.OriginalStake: {Proposal?.OriginalStake}, EventArgs: {e.Stake})");
            System.Diagnostics.Debug.WriteLine($"[MAIN] BuyTime da simulação: {simulationBuyTime:HH:mm:ss.fff}");

            System.Diagnostics.Debug.WriteLine($"[MAIN] Chamando Simulation.AddTransaction...");
            Simulation.AddTransaction(simulatedTransaction);
            System.Diagnostics.Debug.WriteLine($"[MAIN] Transação simulada adicionada à tabela Simulação - Total: {Simulation.SimulationTransactions.Count}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] ======= EVENTO CONTRACTSIMULATED PROCESSADO COM SUCESSO =======");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error handling contract simulation: {ex.Message}");
        }
    }

    private void OnClearTablesRequested(object? sender, EventArgs e)
    {
        if (_disposed) return;

        try
        {
            System.Diagnostics.Debug.WriteLine($"[MAIN] 🧹 Limpando tabelas Simulação e Compras conforme solicitado");
            
            // Limpar tabela de simulação
            Simulation?.ClearTransactions();
            
            // Limpar tabela de compras
            Purchase?.ClearTransactions();
            
            System.Diagnostics.Debug.WriteLine($"[MAIN] ✅ Tabelas limpas com sucesso");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error clearing tables: {ex.Message}");
        }
    }

    private void OnTriggerRealPurchase(object? sender, EventArgs e)
    {
        if (_disposed) return;

        try
        {
            System.Diagnostics.Debug.WriteLine($"[MAIN] ⚡ TRIGGER PARA COMPRA REAL RECEBIDO! Sender: {sender?.GetType().Name}");
            System.Diagnostics.Debug.WriteLine($"[MAIN] ⚡ TRIGGER PARA COMPRA REAL ATIVADO! Executando compra automaticamente...");

            var proposalVM = Proposal as ProposalViewModel;
            bool shouldContinueSimulation = proposalVM?.IsContinuous == true;
            
            System.Diagnostics.Debug.WriteLine($"[MAIN] ProposalVM disponível: {proposalVM != null}, IsContinuous: {shouldContinueSimulation}");

            // Se toggle "Manter" não estiver ativado, parar simulação antes da compra real
            if (proposalVM != null && proposalVM.IsAutoSimulating && !shouldContinueSimulation)
            {
                System.Diagnostics.Debug.WriteLine($"[MAIN] 🛑 Parando simulação automática antes da compra real (Manter=OFF)");
                _ = Task.Run(() => proposalVM.SimulateCommand.Execute(null)); // Parar simulação
            }
            else if (shouldContinueSimulation)
            {
                System.Diagnostics.Debug.WriteLine($"[MAIN] 🔄 Mantendo simulação automática ativa (Manter=ON)");
            }

            // Executar compra real usando o comando do ProposalViewModel
            _ = Task.Run(async () =>
            {
                try
                {
                    // Se parou simulação, aguardar um pouco mais para garantir estabilidade
                    if (!shouldContinueSimulation)
                    {
                        await Task.Delay(1000);
                    }
                    
                    // Garantir que há proposta válida antes de tentar comprar
                    System.Diagnostics.Debug.WriteLine($"[MAIN] 🔄 Forçando nova proposta antes da compra real...");
                    
                                        // Verificar se proposta já está válida antes de solicitar nova
                    var canExecute = Proposal?.BuyCommand?.CanExecute(null) == true;
                    var hasPayout = (Proposal?.Payout ?? 0) > 0;
                    var isNotLoading = Proposal?.IsLoading != true;
                    
                    System.Diagnostics.Debug.WriteLine($"[MAIN] 🔍 Estado atual - CanExecute: {canExecute}, Payout: {Proposal?.Payout ?? 0}, Loading: {!isNotLoading}");
                    
                    if (!canExecute || !hasPayout || !isNotLoading)
                    {
                        System.Diagnostics.Debug.WriteLine($"[MAIN] 🔄 Solicitando nova proposta...");
                        
                        // Solicitar nova proposta apenas se necessário
                        if (Proposal?.CalculateProposalCommand?.CanExecute(null) == true)
                        {
                            await Task.Run(() => Proposal.CalculateProposalCommand.Execute(null));
                            
                            // Aguardar proposta ser calculada (reduzido de 2000ms para 1000ms)
                            await Task.Delay(1000);
                            
                            // Aguardar até que a proposta esteja pronta (máximo 5 segundos)
                            int attempts = 0;
                            const int maxAttempts = 10;
                            
                            while (attempts < maxAttempts)
                            {
                                canExecute = Proposal?.BuyCommand?.CanExecute(null) == true;
                                hasPayout = (Proposal?.Payout ?? 0) > 0;
                                isNotLoading = Proposal?.IsLoading != true;
                                
                                System.Diagnostics.Debug.WriteLine($"[MAIN] 🔍 Tentativa {attempts + 1}/{maxAttempts} - CanExecute: {canExecute}, Payout: {Proposal?.Payout ?? 0}, Loading: {!isNotLoading}");
                                
                                if (canExecute && hasPayout && isNotLoading)
                                {
                                    break;
                                }
                                
                                attempts++;
                                await Task.Delay(200); // Reduzido de 500ms para 200ms
                            }
                            
                            if (attempts >= maxAttempts)
                            {
                                System.Diagnostics.Debug.WriteLine($"[MAIN] ❌ Timeout: Não foi possível obter proposta válida após {maxAttempts} tentativas");
                                return;
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"[MAIN] ❌ Não foi possível solicitar nova proposta - CalculateProposalCommand não disponível");
                            return;
                        }
                    }
                    
                    // Executar compra com proposta válida
                    System.Diagnostics.Debug.WriteLine($"[MAIN] 🚀 Executando compra automática via sistema de martingale...");
                    
                    await Application.Current.Dispatcher.InvokeAsync(async () =>
                    {
                        if (Proposal?.BuyCommand?.CanExecute(null) == true)
                        {
                            // NOVA LÓGICA: Verificar se deve usar martingale ou compra normal
                            bool shouldUseMartingale = Purchase.ShouldApplyMartingale();
                            System.Diagnostics.Debug.WriteLine($"[MAIN] 🎯 Análise: ShouldUseMartingale = {shouldUseMartingale}");
                            
                            bool success = false;
                            
                            if (shouldUseMartingale)
                            {
                                System.Diagnostics.Debug.WriteLine($"[MAIN] 🔥 Aplicando MARTINGALE na compra automática!");
                                // CORREÇÃO: Usar o base stake original, não o stake atual da proposta
                                decimal originalBaseStake = Proposal?.OriginalStake ?? 0.35m;
                                System.Diagnostics.Debug.WriteLine($"[MAIN] 🔧 Usando base stake original: {originalBaseStake} (não {Proposal.Stake})");
                                success = await Purchase.TryExecuteMartingaleEntryAsync(originalBaseStake, Proposal);
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"[MAIN] ✨ Fazendo compra NORMAL (primeira ou após vitória)!");
                                // CORREÇÃO: Para compra normal, também usar o base stake original
                                decimal originalBaseStake = Proposal?.OriginalStake ?? 0.35m;
                                System.Diagnostics.Debug.WriteLine($"[MAIN] 🔧 Usando base stake original: {originalBaseStake} (não {Proposal.Stake})");
                                success = await Purchase.ExecuteNormalPurchaseAsync(originalBaseStake, Proposal);
                            }
                            
                            if (success)
                            {
                                System.Diagnostics.Debug.WriteLine($"[MAIN] ✅ Compra real executada automaticamente! Simulação contínua: {shouldContinueSimulation}");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"[MAIN] ❌ Compra automática rejeitada pelo sistema martingale (contrato ativo) - entrada será executada após expiração");
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"[MAIN] ❌ BuyCommand não pode ser executado no momento da compra");
                        }
                    });
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[MAIN] ❌ Error executing real purchase: {ex.Message}");
                }
            });
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[MAIN] ❌ Error triggering real purchase: {ex.Message}");
        }
    }

    private void OnProfitTableReceived(object? sender, Excalibur.Core.Models.DTOs.ApiResponses.ProfitTableTransaction tx)
    {
        if (_disposed || tx == null) return;

        try
        {
            // Log para diagnóstico - adicionando mais detalhes
            System.Diagnostics.Debug.WriteLine($"PROFIT_TABLE: ContractID={tx.ContractIdString}, TransactionID={tx.TransactionIdString}, PurchaseTime={tx.PurchaseTime}, Profit={tx.Profit}");

            // Converter timestamp para DateTime para comparação
            var txDateTime = DateTimeOffset.FromUnixTimeSeconds(tx.PurchaseTime).DateTime;
            
            // Verificar se o contrato é recente (nas últimas 24 horas)
            var isRecent = (DateTime.UtcNow - txDateTime).TotalHours < 24;
            
            // Para transações recentes, imprimir todos os detalhes
            if (isRecent)
            {
                System.Diagnostics.Debug.WriteLine($"PROFIT_TABLE RECENTE: TX DateTime={txDateTime}, Profit={tx.Profit}");
                
                // Mostrar todos os contratos disponíveis para comparação - thread safe copy
                var transactions = Purchase?.PurchaseTransactions?.ToList();
                if (transactions != null)
                {
                    foreach (var contract in transactions)
                    {
                        System.Diagnostics.Debug.WriteLine($"CONTRATO LOCAL: RefId={contract.RefId}, BuyTime={contract.BuyTime.ToUniversalTime()}, Profit={contract.TotalProfitLoss}");
                    }
                }
            }

            // 1. Tentar correspondência direta por ID
            if (!string.IsNullOrEmpty(tx.TransactionIdString))
            {
                Purchase?.UpdateProfitFromApi(tx.TransactionIdString, tx.Profit);
            }

            if (!string.IsNullOrEmpty(tx.ContractIdString) && tx.ContractIdString != tx.TransactionIdString)
            {
                Purchase?.UpdateProfitFromApi(tx.ContractIdString, tx.Profit);
            }
            
            // 2. Tentar correspondência por horário (quando o ID não funciona)
            // Isso ajuda quando IDs ficam dessincronizados
            if (isRecent)
            {
                Purchase?.UpdateProfitFromApiByTime(txDateTime, tx.Profit, 30);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error processing profit table: {ex.Message}");
        }
    }

    private void OnOpenContractReceived(object? sender, Excalibur.Core.Models.DTOs.ApiResponses.OpenContractDetails contract)
    {
        if (_disposed || contract == null) return;

        try
        {
            // Atualizar usando tanto contractId quanto transactionId para garantir que encontrará
            var refId = !string.IsNullOrEmpty(contract.TransactionIdString) ? contract.TransactionIdString : contract.ContractIdString;
            
            System.Diagnostics.Debug.WriteLine($"CONTRACT DETAILS: ContractID={contract.ContractIdString}, TransactionID={contract.TransactionIdString}, P/L={contract.Profit}, Status={contract.Status}");
            
            // Aplicar o lucro/prejuízo oficial, independentemente do status do contrato
            // Isso garante que usaremos sempre o valor oficial, mesmo se ainda estiver aberto
            if (!string.IsNullOrEmpty(refId))
            {
                Purchase?.UpdateProfitFromApi(refId, contract.Profit);
            }
            
            // Se não encontrou pelo ID principal, tentar o ID alternativo
            if (!string.IsNullOrEmpty(contract.TransactionIdString) && 
                !string.IsNullOrEmpty(contract.ContractIdString) && 
                contract.TransactionIdString != contract.ContractIdString)
            {
                Purchase?.UpdateProfitFromApi(contract.ContractIdString, contract.Profit);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error processing contract details: {ex.Message}");
        }
    }
    
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;

            // Stop and dispose timer first to prevent new executions
            _profitTableTimer?.Stop();
            _profitTableTimer?.Dispose();

            // Unsubscribe from events to prevent memory leaks
            if (Purchase != null)
            {
                Purchase.ContractExpired -= OnContractExpired;
                Purchase.MartingaleReset -= OnMartingaleReset;
            }

            if (Contracts != null)
            {
                Contracts.ContractSelected -= OnContractSelected;
            }

            if (Proposal != null)
            {
                Proposal.ContractPurchased -= OnContractPurchased;
                Proposal.ContractIdUpdated -= OnContractIdUpdated;
                Proposal.ContractSimulated -= OnContractSimulated;
                Proposal.ClearTablesRequested -= OnClearTablesRequested;
            }

            if (Simulation != null)
            {
                Simulation.TriggerRealPurchase -= OnTriggerRealPurchase;
            }

            if (_derivApiService != null)
            {
                _derivApiService.TickReceived -= OnTickReceived;
                _derivApiService.ProfitTableReceived -= OnProfitTableReceived;
                _derivApiService.OpenContractReceived -= OnOpenContractReceived;
            }

            // Dispose semaphore
            _timerSemaphore?.Dispose();

            // Dispose ViewModels if they implement IDisposable
            (AccountInfo as IDisposable)?.Dispose();
            (Purchase as IDisposable)?.Dispose();
            (Simulation as IDisposable)?.Dispose();

            GC.SuppressFinalize(this);
        }
    }
}

public class ContractPurchasedEventArgs : EventArgs
{
    public string ContractType { get; set; } = string.Empty;
    public string ContractId { get; set; } = string.Empty; // Será o proposal_id inicialmente, depois atualizado para transaction_id
    public decimal StartSpot { get; set; }
    public decimal Stake { get; set; }
    public decimal Payout { get; set; }
    public int Duration { get; set; }
    public string DurationType { get; set; } = string.Empty;
}