﻿using System.Globalization;
using System.Windows.Data;

namespace Excalibur.Converters;

public class CurrencyFormatterConverter : IMultiValueConverter
{
    public object Convert (object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        if (values.Length >= 2 && values[0] is decimal balance && values[1] is string currency)
        {
            return $"{balance:N2} {currency}";
        }

        return "0.00 USD";
    }

    public object[] ConvertBack (object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}