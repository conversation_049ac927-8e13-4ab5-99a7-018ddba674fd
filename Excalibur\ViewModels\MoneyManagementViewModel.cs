using System.Globalization;

namespace Excalibur.ViewModels;

public class MoneyManagementViewModel : BaseViewModel
{
    public MoneyManagementViewModel()
    {
        // Carregar configurações salvas se disponíveis
        LoadSettings();
    }

    private string _factorText = "2";
    public string FactorText
    {
        get => _factorText;
        set
        {
            if (_factorText != value)
            {
                _factorText = value;
                OnPropertyChanged();
            }
        }
    }

    private string _levelText = "1";
    public string LevelText
    {
        get => _levelText;
        set
        {
            if (_levelText != value)
            {
                _levelText = value;
                OnPropertyChanged();
            }
        }
    }

    private bool _martingaleEnabled = true;
    public bool MartingaleEnabled
    {
        get => _martingaleEnabled;
        set
        {
            if (_martingaleEnabled != value)
            {
                _martingaleEnabled = value;
                OnPropertyChanged();
            }
        }
    }

    // Novos campos: Amostra, Win e Loss
    private string _amostraText = "5";
    public string AmostraText
    {
        get => _amostraText;
        set
        {
            if (_amostraText != value)
            {
                _amostraText = value;
                OnPropertyChanged();
                UpdateLossFromAmostraAndWin(); // Atualizar Loss quando Amostra muda
            }
        }
    }

    private string _winText = "2";
    public string WinText
    {
        get => _winText;
        set
        {
            if (_winText != value)
            {
                _winText = value;
                OnPropertyChanged();
                UpdateLossFromAmostraAndWin(); // Atualizar Loss quando Win muda
            }
        }
    }

    private string _lossText = "3";
    public string LossText
    {
        get => _lossText;
        set
        {
            if (_lossText != value)
            {
                _lossText = value;
                OnPropertyChanged();
                UpdateWinFromAmostraAndLoss(); // Atualizar Win quando Loss muda
            }
        }
    }

    private bool _isUpdating = false; // Flag para evitar loops infinitos

    // Save configuration property
    private bool _saveConfiguration = false;
    public bool SaveConfiguration
    {
        get => _saveConfiguration;
        set
        {
            if (_saveConfiguration != value)
            {
                _saveConfiguration = value;
                OnPropertyChanged();
                
                if (value)
                {
                    SaveSettings();
                }
            }
        }
    }

    // Métodos para cálculo automático
    private void UpdateLossFromAmostraAndWin()
    {
        if (_isUpdating) return; // Evitar loops infinitos
        
        _isUpdating = true;
        try
        {
            if (int.TryParse(_amostraText, out int amostra) && int.TryParse(_winText, out int win))
            {
                // Garantir que os valores sejam não-negativos
                amostra = Math.Max(0, amostra);
                win = Math.Max(0, Math.Min(win, amostra)); // Win não pode ser maior que Amostra
                
                int calculatedLoss = amostra - win;
                _lossText = calculatedLoss.ToString();
                OnPropertyChanged(nameof(LossText));
                
                // Se Win foi ajustado, atualizar também
                if (win != int.Parse(_winText))
                {
                    _winText = win.ToString();
                    OnPropertyChanged(nameof(WinText));
                }
            }
        }
        finally
        {
            _isUpdating = false;
        }
    }

    private void UpdateWinFromAmostraAndLoss()
    {
        if (_isUpdating) return; // Evitar loops infinitos
        
        _isUpdating = true;
        try
        {
            if (int.TryParse(_amostraText, out int amostra) && int.TryParse(_lossText, out int loss))
            {
                // Garantir que os valores sejam não-negativos
                amostra = Math.Max(0, amostra);
                loss = Math.Max(0, Math.Min(loss, amostra)); // Loss não pode ser maior que Amostra
                
                int calculatedWin = amostra - loss;
                _winText = calculatedWin.ToString();
                OnPropertyChanged(nameof(WinText));
                
                // Se Loss foi ajustado, atualizar também
                if (loss != int.Parse(_lossText))
                {
                    _lossText = loss.ToString();
                    OnPropertyChanged(nameof(LossText));
                }
            }
        }
        finally
        {
            _isUpdating = false;
        }
    }

    // Conveniência para obter valores numéricos (parse seguro)
    public decimal Factor
    {
        get => decimal.TryParse(_factorText, NumberStyles.Any, CultureInfo.InvariantCulture, out var v) ? v : 1m;
    }
    
    public int Level
    {
        get => int.TryParse(_levelText, out var v) ? v : 1;
    }

    public int Amostra
    {
        get => int.TryParse(_amostraText, out var v) ? v : 5;
    }

    public int Win
    {
        get => int.TryParse(_winText, out var v) ? v : 2;
    }

    public int Loss
    {
        get => int.TryParse(_lossText, out var v) ? v : 3;
    }

    private void SaveSettings()
    {
        try
        {
            var settings = Properties.Settings.Default;
            
            // Salvar configurações do Money Management
            settings.MoneyManagementFactorText = FactorText;
            settings.MoneyManagementLevelText = LevelText;
            settings.MoneyManagementMartingaleEnabled = MartingaleEnabled;
            settings.MoneyManagementAmostraText = AmostraText;
            settings.MoneyManagementWinText = WinText;
            settings.MoneyManagementLossText = LossText;
            settings.MoneyManagementSaveConfiguration = SaveConfiguration;
            
            settings.Save();
            
            System.Diagnostics.Debug.WriteLine("[MONEY-MANAGEMENT] Configurações salvas com sucesso");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[MONEY-MANAGEMENT] Erro ao salvar configurações: {ex.Message}");
        }
    }
    
    private void LoadSettings()
    {
        try
        {
            var settings = Properties.Settings.Default;
            
            // Verificar se deve carregar configurações salvas
            if (settings.MoneyManagementSaveConfiguration)
            {
                // Carregar configurações do Money Management
                FactorText = settings.MoneyManagementFactorText ?? "2";
                LevelText = settings.MoneyManagementLevelText ?? "1";
                MartingaleEnabled = settings.MoneyManagementMartingaleEnabled;
                AmostraText = settings.MoneyManagementAmostraText ?? "5";
                WinText = settings.MoneyManagementWinText ?? "2";
                LossText = settings.MoneyManagementLossText ?? "3";
                _saveConfiguration = settings.MoneyManagementSaveConfiguration;
                
                OnPropertyChanged(nameof(SaveConfiguration));
                
                System.Diagnostics.Debug.WriteLine("[MONEY-MANAGEMENT] Configurações carregadas com sucesso");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("[MONEY-MANAGEMENT] Save Configuration desativado, usando valores padrão");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[MONEY-MANAGEMENT] Erro ao carregar configurações: {ex.Message}");
        }
    }
} 