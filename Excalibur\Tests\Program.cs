using System;
using System.Threading.Tasks;
using Excalibur.Tests;

namespace Excalibur.Tests
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Executando testes do sistema de Martingale...");
            
            try
            {
                var test = new MartingaleTest();
                await test.RunTests();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erro durante os testes: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine("\nPressione qualquer tecla para sair...");
            Console.ReadKey();
        }
    }
}
