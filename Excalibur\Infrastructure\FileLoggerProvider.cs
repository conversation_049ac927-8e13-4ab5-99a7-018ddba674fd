using Microsoft.Extensions.Logging;
using System.IO;

namespace Excalibur.Infrastructure;

public class FileLoggerProvider : ILoggerProvider
{
    private readonly string _filePath;
    private readonly object _lock = new object();

    public FileLoggerProvider(string filePath) => _filePath = filePath;

    public ILogger CreateLogger(string categoryName) => new FileLogger(categoryName, _filePath, _lock);

    public void Dispose() { }

    private class FileLogger : ILogger
    {
        private readonly string _category;
        private readonly string _filePath;
        private readonly object _lock;

        public FileLogger(string category, string filePath, object lockObject)
        {
            _category = category;
            _filePath = filePath;
            _lock = lockObject;
        }

        public IDisposable? BeginScope<TState>(TState state) where TState : notnull => null;
        public bool IsEnabled(LogLevel logLevel) => true;

        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            var msg = $"[{timestamp}] [{logLevel}] {_category}: {formatter(state, exception)}";

            lock (_lock)
            {
                File.AppendAllText(_filePath, msg + Environment.NewLine);
            }
        }
    }
}