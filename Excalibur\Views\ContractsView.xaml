<UserControl x:Class="Excalibur.Views.ContractsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <StackPanel Margin="16,12">
        <!-- Header -->
        <TextBlock Text="Contratos Disponíveis" 
                   FontSize="14"
                   FontWeight="SemiBold"
                   Foreground="#FFCCCCCC"
                   Margin="0,0,0,8"/>
        
        <!-- Count -->
        <TextBlock Text="{Binding Symbols.Count, StringFormat='Total: {0} contratos'}" 
                   Margin="0,0,0,8" 
                   Foreground="#FF888888" 
                   FontSize="12"/>
        
        <!-- Contracts List -->
        <ScrollViewer VerticalScrollBarVisibility="Auto" 
                      MaxHeight="350">
            <ItemsControl ItemsSource="{Binding Symbols}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <StackPanel Margin="0,1">
                            <!-- Main Symbol Header -->
                            <Border Background="#FF3A3A3A" 
                                    CornerRadius="4"
                                    BorderBrush="#FF555555"
                                    BorderThickness="1"
                                    Cursor="Hand">
                                <Border.InputBindings>
                                    <MouseBinding MouseAction="LeftClick" 
                                                  Command="{Binding DataContext.ToggleSymbolCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                  CommandParameter="{Binding}"/>
                                </Border.InputBindings>
                                
                                <Grid Margin="8,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <!-- Expand/Collapse Icon -->
                                    <TextBlock Grid.Column="0"
                                               Margin="0,0,8,0"
                                               VerticalAlignment="Center"
                                               FontSize="10"
                                               Foreground="#FF888888">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Text" Value="▶"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsExpanded}" Value="True">
                                                        <Setter Property="Text" Value="▼"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                    
                                    <!-- Symbol Name -->
                                    <TextBlock Grid.Column="1"
                                               Text="{Binding DisplayName}" 
                                               Foreground="#FFCCCCCC"
                                               FontSize="12"
                                               VerticalAlignment="Center"/>
                                    
                                    <!-- Loading Indicator -->
                                    <TextBlock Grid.Column="2"
                                               Text="⟳" 
                                               Foreground="#FF0078D4"
                                               FontSize="12"
                                               Margin="8,0"
                                               VerticalAlignment="Center">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsLoadingContracts}" Value="True">
                                                        <Setter Property="Visibility" Value="Visible"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                    
                                    <!-- Market Type -->
                                    <TextBlock Grid.Column="3"
                                               Text="{Binding Market}" 
                                               Foreground="#FF888888"
                                               FontSize="10"
                                               VerticalAlignment="Center"/>
                                </Grid>
                            </Border>
                            
                            <!-- Expanded Contracts -->
                            <ItemsControl ItemsSource="{Binding AvailableContracts}"
                                          Margin="16,0,0,0">
                                <ItemsControl.Style>
                                    <Style TargetType="ItemsControl">
                                        <Setter Property="Visibility" Value="Collapsed"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsExpanded}" Value="True">
                                                <Setter Property="Visibility" Value="Visible"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ItemsControl.Style>
                                
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Margin="0,1" 
                                                Padding="8,3" 
                                                Background="#FF2A2A2A" 
                                                CornerRadius="3"
                                                BorderBrush="#FF444444"
                                                BorderThickness="1"
                                                Cursor="Hand">
                                            <Border.InputBindings>
                                                <MouseBinding MouseAction="LeftClick" 
                                                              Command="{Binding DataContext.SelectContractCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                              CommandParameter="{Binding}"/>
                                            </Border.InputBindings>
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                
                                                <StackPanel Grid.Column="0">
                                                    <TextBlock Text="{Binding ContractDisplay}" 
                                                               Foreground="#FFB8B8B8"
                                                               FontSize="11"
                                                               FontWeight="Medium"/>
                                                    <TextBlock Text="{Binding ContractCategoryDisplay}" 
                                                               Foreground="#FF888888"
                                                               FontSize="9"
                                                               Margin="0,2,0,0"/>
                                                </StackPanel>
                                                
                                                <StackPanel Grid.Column="1"
                                                           HorizontalAlignment="Center">
                                                    <TextBlock Text="{Binding Sentiment}" 
                                                               Foreground="#FF0078D4"
                                                               FontSize="10"
                                                               HorizontalAlignment="Center"/>
                                                    <TextBlock Text="{Binding ExpiryType}" 
                                                               Foreground="#FF666666"
                                                               FontSize="8"
                                                               HorizontalAlignment="Center"
                                                               Margin="0,2,0,0"/>
                                                </StackPanel>
                                                
                                                <StackPanel Grid.Column="2"
                                                           HorizontalAlignment="Right">
                                                    <TextBlock Text="{Binding MinContractDuration, StringFormat='Min: {0}'}" 
                                                               Foreground="#FF666666"
                                                               FontSize="9"
                                                               HorizontalAlignment="Right"/>
                                                    <TextBlock Text="{Binding MaxContractDuration, StringFormat='Max: {0}'}" 
                                                               Foreground="#FF666666"
                                                               FontSize="9"
                                                               HorizontalAlignment="Right"
                                                               Margin="0,1,0,0"/>
                                                </StackPanel>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
    </StackPanel>
</UserControl> 