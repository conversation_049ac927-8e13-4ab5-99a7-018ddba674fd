using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Text.Json.Serialization;

namespace Excalibur.Core.Models.DTOs.ApiResponses;

public class ActiveSymbol : INotifyPropertyChanged
{
    private bool _isExpanded;
    private bool _isLoadingContracts;

    [JsonPropertyName("display_name")]
    public string DisplayName { get; set; } = string.Empty;

    [JsonPropertyName("symbol")]
    public string Symbol { get; set; } = string.Empty;

    [JsonPropertyName("market")]
    public string Market { get; set; } = string.Empty;

    [JsonPropertyName("submarket")]
    public string Submarket { get; set; } = string.Empty;

    [JsonPropertyName("symbol_type")]
    public string SymbolType { get; set; } = string.Empty;

    public bool IsExpanded
    {
        get => _isExpanded;
        set
        {
            if (_isExpanded != value)
            {
                _isExpanded = value;
                OnPropertyChanged();
            }
        }
    }

    public bool IsLoadingContracts
    {
        get => _isLoadingContracts;
        set
        {
            if (_isLoadingContracts != value)
            {
                _isLoadingContracts = value;
                OnPropertyChanged();
            }
        }
    }

    public ObservableCollection<ContractType> AvailableContracts { get; } = new();

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}