﻿<Application x:Class="Excalibur.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019"
             xmlns:converters="clr-namespace:Excalibur.Converters">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ui:ThemeResources />
                <ui:XamlControlsResources />
            </ResourceDictionary.MergedDictionaries>

            <!-- Converters -->
            <converters:AccountTypeToColorConverter x:Key="AccountTypeToColorConverter"/>
            <converters:CurrencyFormatterConverter x:Key="CurrencyFormatterConverter"/>
            <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>