﻿using Excalibur.Core.Models.DTOs.ApiResponses;

namespace Excalibur.Core.Interfaces;

public interface IDerivApiService
{
    // Eventos
    event EventHandler<AccountInfo>? AccountInfoReceived;
    event EventHandler<string>? ErrorOccurred;
    event EventHandler<bool>? ConnectionStateChanged;
    event EventHandler<List<ActiveSymbol>>? ActiveSymbolsReceived;
    event EventHandler<(string Symbol, List<ContractType> Contracts)>? ContractsForReceived;
    event EventHandler<ProposalResponse>? ProposalReceived;
    event EventHandler<TickData>? TickReceived;
    event EventHandler<string>? BuyCompleted;
    event EventHandler<decimal>? BalanceUpdated;
    event EventHandler<ProfitTableTransaction>? ProfitTableReceived;
    event EventHandler<OpenContractDetails>? OpenContractReceived;

    // Métodos
    Task<bool> ConnectAsync ();
    Task DisconnectAsync ();
    Task<bool> AuthorizeAsync (string token);
    Task<AccountInfo?> GetAccountInfoAsync ();
    Task RequestActiveSymbolsAsync();
    Task RequestContractsForAsync(string symbol);
    Task RequestProposalAsync(string symbol, string contractType, decimal amount, int duration, string durationUnit, string? barrier = null);
    Task RequestTicksStreamAsync(string symbol);
    Task BuyContractAsync(string proposalId, decimal price);
    Task RequestProfitTableAsync();
    Task RequestContractDetailsAsync(string contractId);
    int GetNextRequestId ();

    // Propriedades
    bool IsConnected { get; }
    string AppId { get; }
    string? Token { get; }
}