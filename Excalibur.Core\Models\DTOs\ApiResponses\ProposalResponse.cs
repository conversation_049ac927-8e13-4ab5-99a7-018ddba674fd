using System.Text.Json.Serialization;

namespace Excalibur.Core.Models.DTOs.ApiResponses;

public class ProposalResponse
{
    [JsonPropertyName("proposal")]
    public ProposalDetails Proposal { get; set; } = new();

    [JsonPropertyName("req_id")]
    public int ReqId { get; set; }
}

public class ProposalDetails
{
    [JsonPropertyName("ask_price")]
    public decimal AskPrice { get; set; }

    [JsonPropertyName("payout")]
    public decimal Payout { get; set; }

    [JsonPropertyName("spot")]
    public decimal Spot { get; set; }

    [JsonPropertyName("spot_time")]
    public long SpotTime { get; set; }

    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("longcode")]
    public string LongCode { get; set; } = string.Empty;

    [JsonPropertyName("display_value")]
    public string DisplayValue { get; set; } = string.Empty;

    [JsonPropertyName("multiplier")]
    public decimal? Multiplier { get; set; }

    [Json<PERSON>ropertyName("commission")]
    public decimal? Commission { get; set; }

    [JsonPropertyName("limit_order")]
    public object? LimitOrder { get; set; }

    [JsonPropertyName("cancellation")]
    public object? Cancellation { get; set; }

    [JsonPropertyName("display_number_of_contracts")]
    public string? DisplayNumberOfContracts { get; set; }
}