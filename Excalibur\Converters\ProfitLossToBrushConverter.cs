using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace Excalibur.Converters;

public class ProfitLossToBrushConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is decimal pl)
        {
            return pl >= 0 ? new SolidColorBrush(Colors.LawnGreen) : new SolidColorBrush(Colors.OrangeRed);
        }
        return new SolidColorBrush(Colors.Gray);
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
} 