using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Linq;
using System;

namespace Excalibur.ViewModels;

public class LogViewModel : INotifyPropertyChanged
{
    private const int MAX_LOG_MESSAGES = 100; // Limite para prevenir vazamento de memória
    public ObservableCollection<string> Messages { get; } = new();

    public string All => string.Join("\n", Messages);

    public void Add(string message)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            Messages.Add($"{DateTime.Now:HH:mm:ss} - {message}");
            
            // Manter apenas as últimas MAX_LOG_MESSAGES mensagens
            while (Messages.Count > MAX_LOG_MESSAGES)
            {
                Messages.RemoveAt(0);
            }
            
            OnPropertyChanged(nameof(All));
        });
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
} 