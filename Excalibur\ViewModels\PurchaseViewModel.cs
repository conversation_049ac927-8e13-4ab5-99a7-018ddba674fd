using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Linq;
using System.Timers;
using System.Collections.Generic;
using Excalibur.Models;
using Excalibur.Services;
using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Logging;

namespace Excalibur.ViewModels;

public class PurchaseViewModel : BaseViewModel, IDisposable
{
    public event EventHandler<ContractTransaction>? ContractExpired;
    public event EventHandler<MartingaleResetEventArgs>? MartingaleReset;
    private readonly ILogger<PurchaseViewModel> _logger;
    private ObservableCollection<ContractTransaction> _purchaseTransactions;
    private int _nextTransactionId = 1;
    private System.Timers.Timer _expirationTimer;
    private decimal _currentSpot;
    private readonly object _lock = new object(); // Objeto de sincronização
    private const int MAX_TRANSACTIONS = 50; // Limite para prevenir acúmulo excessivo
    private readonly MartingaleService _martingaleService;
    private MoneyManagementViewModel? _moneyManagementViewModel;
    private readonly HashSet<string> _processedContracts = new HashSet<string>(); // Para evitar processamento duplo

    public PurchaseViewModel(ILogger<PurchaseViewModel> logger, MoneyManagementViewModel? moneyManagementViewModel = null, MartingaleService? martingaleService = null)
    {
        _logger = logger;
        _purchaseTransactions = new ObservableCollection<ContractTransaction>();
        
        // Usar MartingaleService fornecido ou criar um novo
        _martingaleService = martingaleService ?? new MartingaleService();
        
        // Conectar evento de reset do martingale
        _martingaleService.MartingaleReset += OnMartingaleReset;
        
        _moneyManagementViewModel = moneyManagementViewModel;
        
        _logger.LogInformation("[PURCHASE] ⚡ Iniciando PurchaseViewModel...");
        
        // Timer para verificar expiração de contratos a cada segundo
        try
        {
            _expirationTimer = new System.Timers.Timer(1000);
            _expirationTimer.Elapsed += CheckContractExpirations;
            _expirationTimer.AutoReset = true;
            _expirationTimer.Start();
            
            _logger.LogInformation("[PURCHASE] ✅ Timer iniciado com sucesso! Intervalo: {Interval}ms", _expirationTimer.Interval);
            _logger.LogInformation("[PURCHASE] ✅ Timer.Enabled: {Enabled}", _expirationTimer.Enabled);
            _logger.LogInformation("[PURCHASE] ✅ Timer.AutoReset: {AutoReset}", _expirationTimer.AutoReset);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[PURCHASE] ❌ Erro ao iniciar timer: {Message}", ex.Message);
        }
    }

    public ObservableCollection<ContractTransaction> PurchaseTransactions
    {
        get => _purchaseTransactions;
        set
        {
            _purchaseTransactions = value;
            OnPropertyChanged();
        }
    }

    // Propriedades calculadas para estatísticas
    public int TotalPurchaseEntries => _purchaseTransactions?.Count ?? 0;
    
    public decimal TotalProfitLoss 
    { 
        get 
        { 
            var total = _purchaseTransactions?.Sum(t => t.TotalProfitLoss) ?? 0;
            _logger.LogDebug("[PURCHASE] Calculando TotalProfitLoss: {Total:F2}", total);
            
            // Debug: listar todas as transações e seus valores
            if (_purchaseTransactions != null)
            {
                foreach (var t in _purchaseTransactions)
                {
                    _logger.LogDebug("[PURCHASE] Transação ID={Id}, RefId={RefId}, TotalProfitLoss={TotalProfitLoss:F2}", t.Id, t.RefId, t.TotalProfitLoss);
                }
            }
            
            return total;
        }
    }

    public void AddTransaction(ContractTransaction transaction)
    {
        // Garantir que a adição seja feita no thread correto
        if (System.Windows.Application.Current?.Dispatcher.CheckAccess() == false)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() => AddTransaction(transaction));
            return;
        }

        lock (_lock)
        {
            // Atribuir ID se não tiver
            if (transaction.Id == 0)
            {
                transaction.Id = _nextTransactionId++;
            }
            
            _logger.LogInformation("[PURCHASE] 📝 Adicionando transação: ID={Id}, RefId={RefId}, Type={Type}, Stake={Stake}, Duration={Duration}{DurationType}", 
                transaction.Id, transaction.RefId, transaction.Type, transaction.Stake, transaction.Duration, transaction.DurationType);
            
            // Garantir que a transação seja marcada como ativa
            if (string.IsNullOrEmpty(transaction.RefId))
            {
                _logger.LogWarning("[PURCHASE] ⚠️ Transação sem RefId! Tipo: {Type}, Stake: {Stake}", transaction.Type, transaction.Stake);
            }
            else
            {
                // CORREÇÃO: Marcar como BUY para compras reais (não apenas verificar se é BUY)
                // Se o RefId contém apenas números, é uma compra real
                if (transaction.RefId.All(char.IsDigit))
                {
                    transaction.IsActive = true;
                    // CORREÇÃO: Não alterar o tipo - manter o nome original do contrato
                    _logger.LogInformation("[PURCHASE] 🔥 Transação marcada como COMPRA REAL ATIVA: RefId={RefId}, Type={Type}", transaction.RefId, transaction.Type);
                    
                    // CORREÇÃO: Não registrar contrato aqui para evitar registros duplos
                    // O registro será feito apenas em ExecuteNormalPurchase quando a compra for executada
                    _logger.LogInformation("[PURCHASE] 📝 Transação de compra real adicionada: RefId={RefId}, Type={Type} (registro no martingale será feito na execução)", transaction.RefId, transaction.Type);
                }
                else
                {
                    _logger.LogInformation("[PURCHASE] 📝 Transação de simulação adicionada: RefId={RefId}, Type={Type}", transaction.RefId, transaction.Type);
                }
            }
            
            // Garantir que os dados de expiração estejam corretos
            if (transaction.Duration <= 0)
            {
                transaction.Duration = 2; // Padrão: 2 ticks
                transaction.DurationType = "t";
                _logger.LogWarning("[PURCHASE] ⚠️ Duração não definida, usando padrão: 2t");
            }
            
            if (string.IsNullOrEmpty(transaction.DurationType))
            {
                transaction.DurationType = "t";
                _logger.LogWarning("[PURCHASE] ⚠️ Tipo de duração não definido, usando padrão: t");
            }
            
            var expirationTime = transaction.GetExpirationTimeInSeconds();
            _logger.LogInformation("[PURCHASE] 📝 Transação configurada: Duração={Duration}{DurationType}, Expiração={ExpirationTime}s", 
                transaction.Duration, transaction.DurationType, expirationTime);
            
            _purchaseTransactions.Add(transaction);
            
            // Limitar o número de transações para evitar acúmulo excessivo
            if (_purchaseTransactions.Count > MAX_TRANSACTIONS)
            {
                var oldestTransaction = _purchaseTransactions.First();
                _purchaseTransactions.Remove(oldestTransaction);
                _logger.LogInformation("[PURCHASE] 🧹 Transação mais antiga removida para manter limite de {MaxTransactions}", MAX_TRANSACTIONS);
            }
            
            _logger.LogInformation("[PURCHASE] 📝 Transação adicionada: ID={Id}, RefId={RefId}, BuyTime={BuyTime:HH:mm:ss}", 
                transaction.Id, transaction.RefId, transaction.BuyTime);
        }
        
        // Notificar mudanças nas propriedades
        OnPropertyChanged(nameof(TotalPurchaseEntries));
        OnPropertyChanged(nameof(TotalProfitLoss));
    }

    public void UpdateTransaction(string refId, decimal endSpot, decimal totalProfitLoss, bool isClosed = false)
    {
        var transaction = _purchaseTransactions.FirstOrDefault(t => t.RefId == refId);
        if (transaction != null)
        {
            transaction.EndSpot = endSpot;
            transaction.TotalProfitLoss = totalProfitLoss;
            
            if (isClosed)
            {
                transaction.SellTime = DateTime.Now;
                transaction.IsActive = false;
            }
            
            // Notificar mudança no lucro/prejuízo total
            OnPropertyChanged(nameof(TotalProfitLoss));
        }
    }
    
    public void UpdateContractId(string oldProposalId, string newContractId)
    {
        lock (_lock)
        {
            var transaction = _purchaseTransactions.FirstOrDefault(t => t.RefId == oldProposalId);
            if (transaction != null)
            {
                transaction.RefId = newContractId;
                _logger.LogInformation("[PURCHASE] RefId atualizado: {Old} -> {New}", oldProposalId, newContractId);
                
                // IMPORTANTE: Informar ao MartingaleService sobre o novo Contract ID
                // Se o contrato ativo no martingale era a proposta antiga, atualizar para o novo contract ID
                if (_moneyManagementViewModel?.MartingaleEnabled == true)
                {
                    var currentActiveId = _martingaleService.GetActiveContractId();
                    if (currentActiveId == oldProposalId)
                    {
                        // Atualizar o ID do contrato ativo no martingale para o novo contract ID
                        _martingaleService.UpdateActiveContractId(newContractId);
                        _logger.LogInformation("[PURCHASE] MartingaleService: Active Contract ID atualizado {Old} -> {New}", oldProposalId, newContractId);
                        
                        // LOG INFORMATIVO para aparecer no arquivo log.txt
                        _logger.LogInformation("[PURCHASE] MartingaleService: Active Contract ID atualizado {Old} -> {New}", oldProposalId, newContractId);
                    }
                }
            }
        }
    }

    public void UpdateCurrentSpot(decimal currentSpot)
    {
        _currentSpot = currentSpot;
        _logger.LogInformation("UPDATE_SPOT: Novo spot={Spot}, Contratos ativos={ActiveContracts}", currentSpot, _purchaseTransactions.Count(t => t.IsActive));
        
        lock(_lock) // Bloquear acesso concorrente à coleção
        {
            // Atualizar apenas contratos ativos
            foreach (var transaction in _purchaseTransactions.Where(t => t.IsActive))
            {
                // Atualizar EndSpot a cada tick para refletir a cotação mais recente
                transaction.EndSpot = currentSpot;

                // Se ainda não temos StartSpot válido, capturar agora (primeiro tick)
                if (transaction.StartSpot == 0)
                {
                    transaction.StartSpot = currentSpot;
                }
                
                // Durante a execução do contrato, mostrar P/L em tempo real baseado na variação atual do preço
                if (!transaction.ProfitFromApi && transaction.IsActive)
                {
                    decimal priceDifference = currentSpot - transaction.StartSpot; // diferença com sinal

                    // Determinar direção do contrato (subida ou descida) para calcular sinal correto do lucro
                    var lowerType = transaction.Type.ToLowerInvariant();
                    bool isDescending = lowerType.Contains("lower") || lowerType.Contains("fall") ||
                                        lowerType.Contains("down")   || lowerType.Contains("put");

                    decimal calculatedPl;
                    if (isDescending)
                    {
                        // Para contratos de descida o lucro é positivo quando o preço cai
                        calculatedPl = -priceDifference * transaction.Stake;
                    }
                    else
                    {
                        // Para contratos de subida o lucro é positivo quando o preço sobe
                        calculatedPl = priceDifference * transaction.Stake;
                    }

                    // Atualizar TotalProfitLoss com o valor calculado
                    transaction.TotalProfitLoss = calculatedPl;
                }
            }
        }
        
        // Sempre notificar mudança no lucro/prejuízo total 
        OnPropertyChanged(nameof(TotalProfitLoss));
    }

    private void CheckContractExpirations(object sender, ElapsedEventArgs e)
    {
        var now = DateTime.Now;
        
        // Só log detalhado se há contratos ativos
        var activeContractsCount = _purchaseTransactions.Count(t => t.IsActive);
        if (activeContractsCount > 0)
        {
            _logger.LogInformation("[PURCHASE] ⏰ Timer executado - {Active} contratos ativos", activeContractsCount);
        }
        
        List<ContractTransaction> expiredContracts = new List<ContractTransaction>();
        
        lock(_lock) // Bloquear acesso concorrente à coleção
        {
            // Verificar contratos ativos
            var allActiveContracts = _purchaseTransactions.Where(t => t.IsActive && !string.IsNullOrEmpty(t.RefId)).ToList();
            
            // Log só se há contratos para verificar
            if (allActiveContracts.Count > 0)
            {
                _logger.LogInformation("[PURCHASE] ⏰ Verificando {Count} contratos ativos", allActiveContracts.Count);
            }
            
            foreach (var contract in allActiveContracts)
            {
                // Verificar se já foi processado
                if (_processedContracts.Contains(contract.RefId))
                {
                    continue;
                }
                
                // Verificar se expirou baseado no BuyTime
                var timeElapsed = DateTime.Now - contract.BuyTime;
                var expirationTimeInSeconds = contract.GetExpirationTimeInSeconds();
                bool isExpired = timeElapsed.TotalSeconds >= expirationTimeInSeconds;
                
                if (isExpired)
                {
                    _logger.LogInformation("[PURCHASE] ⏰ ✅ Contrato {RefId} EXPIRADO! Tempo decorrido: {TimeElapsed}s", 
                        contract.RefId, timeElapsed.TotalSeconds);
                    expiredContracts.Add(contract);
                }
                // Remover log verboso do tempo restante para reduzir processamento
            }
        }
        
        // Log só se encontrou contratos expirados
        if (expiredContracts.Count > 0)
        {
            _logger.LogInformation("[PURCHASE] ⏰ Encontrados {Count} contratos expirados", expiredContracts.Count);
        }
        
        // Processar expirações fora do lock
        foreach (var expiredContract in expiredContracts)
        {
            _logger.LogInformation("[PURCHASE] ⏰ Processando expiração: RefId={RefId}, Type={Type}", expiredContract.RefId, expiredContract.Type);
            
            // Marcar como processado
            _processedContracts.Add(expiredContract.RefId);
            
            // Atualizar contratos no UI thread
            System.Windows.Application.Current?.Dispatcher.Invoke(() =>
            {
                expiredContract.IsActive = false;
                expiredContract.EndSpot = _currentSpot;
                expiredContract.SellTime = DateTime.Now;
                
                // Calcular resultado baseado no tipo de contrato
                bool isWin = expiredContract.IsCurrentlyWinning(_currentSpot);
                
                if (isWin)
                {
                    expiredContract.TotalProfitLoss = expiredContract.Payout - expiredContract.Stake;
                    _logger.LogInformation("[PURCHASE] ✅ Contrato {RefId} GANHOU! P/L={ProfitLoss}", expiredContract.RefId, expiredContract.TotalProfitLoss);
                }
                else
                {
                    expiredContract.TotalProfitLoss = -expiredContract.Stake;
                    _logger.LogInformation("[PURCHASE] ❌ Contrato {RefId} PERDEU! P/L={ProfitLoss}", expiredContract.RefId, expiredContract.TotalProfitLoss);
                }
                
                // Notificar mudanças na UI
                OnPropertyChanged(nameof(TotalProfitLoss));
                OnPropertyChanged(nameof(TotalPurchaseEntries));
            });
            
            // Processar resultado no sistema martingale
            if (_moneyManagementViewModel != null && _moneyManagementViewModel.MartingaleEnabled)
            {
                _logger.LogInformation("[PURCHASE] 🎯 Processando resultado no sistema martingale...");
                ProcessMartingaleResult(expiredContract.IsCurrentlyWinning(_currentSpot), expiredContract.RefId);
            }
            
            // Disparar evento de expiração
            ContractExpired?.Invoke(this, expiredContract);
        }
        
        // Se houve contratos expirados, notificar mudança no total
        if (expiredContracts.Count > 0)
        {
            OnPropertyChanged(nameof(TotalProfitLoss));
            
            // Limpar lista de contratos processados periodicamente (manter só os últimos 100)
            lock (_lock)
            {
                if (_processedContracts.Count > 100)
                {
                    var toRemove = _processedContracts.Take(_processedContracts.Count - 50).ToList();
                    foreach (var id in toRemove)
                    {
                        _processedContracts.Remove(id);
                    }
                    _logger.LogInformation("[PURCHASE] 🧹 Lista de contratos processados limpa. Count: {Count}", _processedContracts.Count);
                }
            }
        }
        
        // Remover log verboso do fim da execução para reduzir processamento
    }

    public void UpdateProfitFromApi(string refId, decimal profit)
    {
        _logger.LogInformation("ATUALIZANDO: Tentando atualizar P/L para RefId={RefId}, Profit={Profit}", refId, profit);
        
        lock(_lock) // Bloquear acesso concorrente à coleção
        {
            var transaction = _purchaseTransactions.FirstOrDefault(t => t.RefId == refId);
            if (transaction != null)
            {
                // CORREÇÃO: Só atualizar se o contrato ainda estiver ativo
                // Se já expirou, não sobrescrever o valor calculado localmente
                if (transaction.IsActive)
                {
                    _logger.LogInformation("ENCONTRADO E ATIVO: RefId={RefId}, P/L anterior={PreviousProfit}, novo P/L={NewProfit}", refId, transaction.TotalProfitLoss, profit);
                    transaction.TotalProfitLoss = profit;
                    transaction.ProfitFromApi = true;
                    // Forçar atualização da UI
                    transaction.OnPropertyChanged(nameof(transaction.TotalProfitLoss));
                    transaction.OnPropertyChanged(nameof(transaction.TotalProfitLossString));
                    
                    // Notificar mudança no total geral
                    OnPropertyChanged(nameof(TotalProfitLoss));
                }
                else
                {
                    _logger.LogInformation("CONTRATO JÁ EXPIRADO: RefId={RefId}, mantendo P/L calculado localmente: {LocalProfit}, ignorando P/L da API: {ApiProfit}", 
                        refId, transaction.TotalProfitLoss, profit);
                }
            }
            else
            {
                _logger.LogInformation("NÃO ENCONTRADO: RefId={RefId}, Profit={Profit}", refId, profit);
                // Listar IDs atuais para debug
                var allIds = string.Join(", ", _purchaseTransactions.Select(t => t.RefId));
                _logger.LogInformation("IDs disponíveis: {Ids}", allIds);
            }
        }
    }
    
    // Método para atualizar por proximidade de hora, quando IDs não são confiáveis
    public void UpdateProfitFromApiByTime(DateTime apiDateTime, decimal profit, int secondsWindow = 30)
    {
        _logger.LogInformation("TENTANDO MATCH POR HORA: API DateTime={ApiDateTime}, Profit={Profit}", apiDateTime, profit);
        
        lock(_lock)
        {
            // Verificar transações que têm horário de compra próximo ao horário da API
            var startWindow = apiDateTime.AddSeconds(-secondsWindow);
            var endWindow = apiDateTime.AddSeconds(secondsWindow);
            
            var matchingTransaction = _purchaseTransactions
                .Where(t => t.BuyTime.ToUniversalTime() >= startWindow && 
                           t.BuyTime.ToUniversalTime() <= endWindow)
                .OrderBy(t => Math.Abs((t.BuyTime.ToUniversalTime() - apiDateTime).TotalSeconds))
                .FirstOrDefault();
                
            if (matchingTransaction != null)
            {
                // CORREÇÃO: Só atualizar se o contrato ainda estiver ativo
                // Se já expirou, não sobrescrever o valor calculado localmente
                if (matchingTransaction.IsActive)
                {
                    _logger.LogInformation("MATCH POR HORA ENCONTRADO E ATIVO: RefId={RefId}, " +
                        "BuyTime={BuyTime}, API DateTime={ApiDateTime}, " +
                        "P/L anterior={PreviousProfit}, novo P/L={NewProfit}", matchingTransaction.RefId, matchingTransaction.BuyTime, apiDateTime, matchingTransaction.TotalProfitLoss, profit);
                    
                    matchingTransaction.TotalProfitLoss = profit;
                    matchingTransaction.ProfitFromApi = true;
                    // Forçar atualização da UI
                    matchingTransaction.OnPropertyChanged(nameof(matchingTransaction.TotalProfitLoss));
                    matchingTransaction.OnPropertyChanged(nameof(matchingTransaction.TotalProfitLossString));
                    
                    // Notificar mudança no total geral
                    OnPropertyChanged(nameof(TotalProfitLoss));
                }
                else
                {
                    _logger.LogInformation("CONTRATO JÁ EXPIRADO POR HORA: RefId={RefId}, mantendo P/L calculado localmente: {LocalProfit}, ignorando P/L da API: {ApiProfit}", 
                        matchingTransaction.RefId, matchingTransaction.TotalProfitLoss, profit);
                }
            }
            else
            {
                _logger.LogInformation("NENHUM MATCH POR HORA: API DateTime={ApiDateTime}, janela={StartWindow} a {EndWindow}", apiDateTime, startWindow, endWindow);
            }
        }
    }

    public void ClearTransactions()
    {
        // Garantir que a limpeza seja feita no thread correto
        if (System.Windows.Application.Current?.Dispatcher.CheckAccess() == false)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() => ClearTransactions());
            return;
        }

        lock (_lock)
        {
            _logger.LogInformation("[PURCHASE] Limpando {Count} transações de compra", _purchaseTransactions.Count);
            _purchaseTransactions.Clear();
            _processedContracts.Clear(); // Limpar contratos processados
            _nextTransactionId = 1; // Reset do contador de IDs
            _logger.LogInformation("[PURCHASE] Tabela de compras limpa");
        }
        
        // Reset do sistema de martingale
        _martingaleService.ResetMartingale();
        _logger.LogInformation("[PURCHASE] Sistema de martingale resetado");
        
        // Notificar mudança na propriedade
        OnPropertyChanged(nameof(PurchaseTransactions));
        OnPropertyChanged(nameof(TotalPurchaseEntries));
        OnPropertyChanged(nameof(TotalProfitLoss));
    }

    public async System.Threading.Tasks.Task<bool> TryExecuteMartingaleEntryAsync(decimal baseStake, ProposalViewModel proposalViewModel)
    {
        _logger.LogInformation("[PURCHASE] 📥 TryExecuteMartingaleEntryAsync chamado! BaseStake: {BaseStake}", baseStake);
        
        if (_moneyManagementViewModel == null || !_moneyManagementViewModel.MartingaleEnabled)
        {
            _logger.LogInformation("[PURCHASE] ❌ Martingale desabilitado ou MoneyManagement null!");
            return false;
        }

        // NOVA LÓGICA: Verificar se pode aceitar entrada
        if (!_martingaleService.CanAcceptNewEntry())
        {
            _logger.LogInformation("[PURCHASE] ❌ ENTRADA REJEITADA! Sistema martingale tem contrato ativo.");
            LogMartingaleSystemState();
            return false;
        }

        _logger.LogInformation("[PURCHASE] ✅ Martingale habilitado e pode aceitar entrada!");
        
        // Log do estado atual do sistema
        LogMartingaleSystemState();
        
        // Inicializar sistema se necessário (apenas se não foi inicializado ainda)
        if (_martingaleService.GetCurrentMartingaleLevel() == 1 && !_martingaleService.HasActiveContract())
        {
            _logger.LogInformation("[PURCHASE] 🔧 Inicializando sistema martingale pela primeira vez com baseStake: {BaseStake}", baseStake);
            InitializeMartingaleSystem(baseStake);
        }
        else
        {
            _logger.LogInformation("[PURCHASE] ⚡ Sistema martingale já inicializado, usando configuração existente");
        }

        // Criar função para executar compra real via API que retorna Contract ID
        Func<decimal, System.Threading.Tasks.Task<string>> executePurchase = async (stake) =>
        {
            _logger.LogInformation("[MARTINGALE-EXEC] 🔥🔥🔥 FUNÇÃO executePurchase CHAMADA! 🔥🔥🔥");
            _logger.LogInformation("[MARTINGALE-EXEC] 🔥 Stake recebido: {Stake}", stake);
            _logger.LogInformation("[MARTINGALE-EXEC] 🔥 ProposalViewModel é null? {IsNull}", proposalViewModel == null);
            
            try
            {
                _logger.LogInformation("[MARTINGALE-EXEC] ✅ Executando compra com stake: {Stake}", stake);
                
                string contractId = string.Empty;
                
                // Usar Dispatcher para executar no thread da UI
                var dispatcher = System.Windows.Application.Current?.Dispatcher;
                if (dispatcher != null)
                {
                    await dispatcher.InvokeAsync(async () =>
                    {
                        try
                        {
                            // Atualizar stake no proposalViewModel
                            _logger.LogInformation("[MARTINGALE-EXEC] 🔧 Atualizando stake para: {Stake}", stake);
                            proposalViewModel.SetStake(stake);
                        
                        // Delay aumentado para garantir que a proposta seja calculada
                        _logger.LogInformation("[MARTINGALE-EXEC] ⏳ Aguardando atualização da proposta...");
                        await System.Threading.Tasks.Task.Delay(500);
                        
                        // Executar compra real via API usando o método original
                        _logger.LogInformation("[MARTINGALE-EXEC] 🚀 Iniciando ExecuteRealPurchaseAsync com stake: {Stake}...", stake);
                        contractId = await proposalViewModel.ExecuteRealPurchaseAsync(stake);
                        
                        _logger.LogInformation("[MARTINGALE-EXEC] ✅ Contract ID retornado: {ContractId}", contractId);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "[MARTINGALE] Erro ao executar compra real: {Message}", ex.Message);
                        }
                    });
                }
                
                return contractId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[MARTINGALE] Erro geral ao executar compra: {Message}", ex.Message);
                return string.Empty;
            }
        };

        // Enfileirar entrada com groupId baseado no tipo de contrato
        string contractType = proposalViewModel.SelectedContract?.ContractTypeValue ?? "Unknown";
        string groupId = $"{contractType}_{DateTime.Now:yyyyMMdd}";
        
        _logger.LogInformation("[PURCHASE] 🔄 Tentando executar entrada no MartingaleService...");
        _logger.LogInformation("[PURCHASE] 🔄 GroupId: {GroupId}", groupId);
        _logger.LogInformation("[PURCHASE] 🔄 BaseStake passado: {BaseStake}", baseStake);
        _logger.LogInformation("[PURCHASE] 🔄 Nível martingale atual: {MartingaleLevel}", _martingaleService.GetCurrentMartingaleLevel());
        
        // Calcular stake esperado para comparação
        var expectedStake = _martingaleService.GetCurrentMartingaleLevel() > 1 ? 
            baseStake * (decimal)Math.Pow((double)_moneyManagementViewModel.Factor, _martingaleService.GetCurrentMartingaleLevel() - 1) : 
            baseStake;
        _logger.LogInformation("[PURCHASE] 🔄 Stake esperado com martingale: {ExpectedStake}", expectedStake);
        
        bool success = await _martingaleService.TryExecuteEntryAsync(baseStake, executePurchase, groupId);
        
        if (success)
        {
            _logger.LogInformation("[PURCHASE] ✅ Entrada executada com sucesso no MartingaleService!");
        }
        else
        {
            _logger.LogInformation("[PURCHASE] ❌ Falha ao executar entrada no MartingaleService!");
        }
        
        return success;
    }

    public void InitializeMartingaleSystem(decimal baseStake)
    {
        if (_moneyManagementViewModel == null || !_moneyManagementViewModel.MartingaleEnabled)
        {
            _logger.LogInformation("[PURCHASE] ❌ Martingale desabilitado ou MoneyManagement null");
            return;
        }

        _logger.LogInformation("[PURCHASE] ⚙️ Inicializando sistema martingale com:");
        _logger.LogInformation("[PURCHASE] ⚙️ Base Stake: {BaseStake}", baseStake);
        _logger.LogInformation("[PURCHASE] ⚙️ Factor: {Factor}", _moneyManagementViewModel.Factor);
        _logger.LogInformation("[PURCHASE] ⚙️ Level: {Level}", _moneyManagementViewModel.Level);

        // NOVA LÓGICA: Usar InitializeMartingaleSettings
        _martingaleService.InitializeMartingaleSettings(
            baseStake,
            _moneyManagementViewModel.Factor,
            _moneyManagementViewModel.Level
        );
        
        // Manter compatibilidade com sistema legado
        _martingaleService.GetOrCreateGlobalMartingaleGroup(
            baseStake,
            _moneyManagementViewModel.Factor,
            _moneyManagementViewModel.Level
        );
        
        _logger.LogInformation("[PURCHASE] ✅ Sistema martingale inicializado com sucesso");
    }

    public void ProcessMartingaleResult(bool isWin, string contractId = "")
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
        var stackTrace = new System.Diagnostics.StackTrace();
        var callingMethod = stackTrace.GetFrame(1)?.GetMethod()?.Name ?? "Unknown";
        
        _logger.LogInformation("[PURCHASE] 🎯 [{Timestamp}] ProcessMartingaleResult chamado! IsWin: {IsWin}, ContractId: {ContractId}", timestamp, isWin, contractId);
        _logger.LogInformation("[PURCHASE] 🎯 [{Timestamp}] Chamado por: {CallingMethod}", timestamp, callingMethod);
        _logger.LogInformation("[PURCHASE] 🎯 [{Timestamp}] Thread: {ThreadId}", timestamp, System.Threading.Thread.CurrentThread.ManagedThreadId);
        
        _martingaleService.ProcessContractExpiration(contractId, isWin);
        
        _logger.LogInformation("[PURCHASE] 🎯 [{Timestamp}] Resultado processado pelo MartingaleService", timestamp);
    }

    public void SetMoneyManagementViewModel(MoneyManagementViewModel moneyManagementViewModel)
    {
        _moneyManagementViewModel = moneyManagementViewModel;
    }

    // Nova função para configurar verificador de critérios de simulação
    public void ConfigureSimulationCriteriaChecker(Func<System.Threading.Tasks.Task<bool>> checker)
    {
        _martingaleService.SetSimulationCriteriaChecker(checker);
        _logger.LogInformation("[PURCHASE] 🔍 Verificador de critérios de simulação configurado no MartingaleService");
    }

    private void LogMartingaleSystemState()
    {
        _logger.LogInformation("[PURCHASE] 📊 Solicitando estado detalhado do sistema martingale...");
        _martingaleService.LogSystemState("PurchaseViewModel");
    }

    private void OnMartingaleReset(object? sender, MartingaleResetEventArgs e)
    {
        _logger.LogInformation("[PURCHASE] 🔔 Martingale resetado! Motivo: {Reason}, Stake original: {OriginalStake}", 
            e.ResetReason, e.OriginalStake);
            
        // Repassar evento para interessados (como ProposalViewModel)
        MartingaleReset?.Invoke(this, e);
    }

    public bool ShouldApplyMartingale()
    {
        if (_moneyManagementViewModel == null || !_moneyManagementViewModel.MartingaleEnabled)
        {
            _logger.LogInformation("[PURCHASE] 🚫 Martingale desabilitado");
            return false;
        }

        // Verificar se há contrato ativo (não pode aplicar durante contrato ativo)
        if (_martingaleService.HasActiveContract())
        {
            _logger.LogInformation("[PURCHASE] ⏳ Contrato ativo - não aplicar martingale agora");
            return false;
        }

        // Aplicar martingale apenas se:
        // 1. Nível atual > 1 (indica que houve perdas anteriores)
        // 2. OU se explicitamente há necessidade de martingale
        int currentLevel = _martingaleService.GetCurrentMartingaleLevel();
        bool needsMartingale = _martingaleService.NeedsMartingale();
        
        bool shouldApply = currentLevel > 1 || needsMartingale;
        
        _logger.LogInformation("[PURCHASE] 🔍 ShouldApplyMartingale: {ShouldApply} (Level: {Level}, NeedsMartingale: {NeedsMartingale})", 
            shouldApply, currentLevel, needsMartingale);
            
        return shouldApply;
    }

    public async System.Threading.Tasks.Task<bool> ExecuteNormalPurchaseAsync(decimal baseStake, ProposalViewModel proposalViewModel)
    {
        _logger.LogInformation("[PURCHASE] 💰 ExecuteNormalPurchaseAsync - Compra normal sem martingale");
        _logger.LogInformation("[PURCHASE] 💰 BaseStake: {BaseStake}", baseStake);

        try
        {
            // Atualizar stake no proposalViewModel para o valor normal
            proposalViewModel.SetStake(baseStake);
            
            // Aguardar atualização da proposta
            await System.Threading.Tasks.Task.Delay(500);
            
            // Executar compra real via API
            string contractId = await proposalViewModel.ExecuteRealPurchaseAsync();
            
            if (!string.IsNullOrEmpty(contractId))
            {
                _logger.LogInformation("[PURCHASE] ✅ Compra normal executada com sucesso! Contract ID: {ContractId}", contractId);
                
                // Registrar o contrato no sistema martingale para controle
                _martingaleService.RegisterActiveContract(contractId);
                
                return true;
            }
            else
            {
                _logger.LogInformation("[PURCHASE] ❌ Falha na compra normal - Contract ID vazio");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[PURCHASE] ❌ Erro ao executar compra normal: {Message}", ex.Message);
            return false;
        }
    }

    public void Dispose()
    {
        try
        {
            _logger.LogInformation("[PURCHASE] 🛑 Disposing PurchaseViewModel...");
            
            _expirationTimer?.Stop();
            _expirationTimer?.Dispose();
            
            // Desconectar evento do MartingaleService
            if (_martingaleService != null)
            {
                _martingaleService.MartingaleReset -= OnMartingaleReset;
            }
            
            _logger.LogInformation("[PURCHASE] ✅ Timer parado e liberado");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[PURCHASE] ❌ Erro ao fazer dispose: {Message}", ex.Message);
        }
    }
} 