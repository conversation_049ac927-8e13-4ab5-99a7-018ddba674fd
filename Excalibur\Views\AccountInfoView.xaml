﻿<UserControl x:Class="Excalibur.Views.AccountInfoView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <DropShadowEffect x:Key="BlueShadowEffect" Color="#FF0078D4" BlurRadius="22" ShadowDepth="0" Opacity="1"/>
        <DropShadowEffect x:Key="RedShadowEffect" Color="#FFDC3545" BlurRadius="28" ShadowDepth="0" Opacity="1"/>
    </UserControl.Resources>

    <Border Background="#FF2D2D30"
            BorderBrush="#FF404040"
            BorderThickness="1"
            CornerRadius="12"
            Width="320"
            Height="150">

        <Border.Effect>
            <DropShadowEffect Color="Black" 
                            Opacity="0.3" 
                            BlurRadius="15" 
                            ShadowDepth="5"/>
        </Border.Effect>

        <Grid Margin="10,8,10,8">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header Row: Account title + Status + Button -->
            <Grid Grid.Row="0" Margin="0,0,0,2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Account Title (esquerda) -->
                <TextBlock Grid.Column="0"
                          Text="Account" 
                          FontSize="16"
                          FontWeight="SemiBold"
                          Foreground="#FFCCCCCC"
                          VerticalAlignment="Top"
                          Margin="0,0,0,0"/>

                <!-- Status + Button (direita) -->
                <StackPanel Grid.Column="1">
                    <!-- Status com indicador -->
                    <StackPanel Orientation="Horizontal" 
                               HorizontalAlignment="Right"
                               Margin="0,0,0,2">
                        <!-- Status Indicator -->
                        <Ellipse Width="8" Height="8" 
                                Margin="0,0,6,0"
                                VerticalAlignment="Center">
                            <Ellipse.Style>
                                <Style TargetType="Ellipse">
                                    <Setter Property="Fill" Value="Red"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsConnected}" Value="True">
                                            <Setter Property="Fill" Value="LimeGreen"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Ellipse.Style>
                        </Ellipse>

                        <TextBlock Text="{Binding ConnectionStatus}" 
                                  FontSize="11"
                                  Foreground="#FF999999"
                                  VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- Connection Button com efeito de luz -->
                    <Button Command="{Binding ConnectCommand}"
                           Height="28"
                           Width="80"
                           BorderThickness="0"
                           FontSize="12"
                           FontWeight="Medium"
                           HorizontalAlignment="Right"
                           Margin="0,24,0,0">

                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="#FF0078D4"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="Content" Value="Connect"/>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                   CornerRadius="3"
                                                   Padding="4,2">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="Transparent"
                                                                     BlurRadius="0"
                                                                     ShadowDepth="0"
                                                                     Opacity="0"/>
                                                </Border.Effect>
                                                <ContentPresenter HorizontalAlignment="Center" 
                                                                VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#FF106EBE"/>
                                                    <Setter Property="Effect" Value="{DynamicResource BlueShadowEffect}"/>
                                                </Trigger>
                                                <Trigger Property="IsPressed" Value="True">
                                                    <Setter Property="Background" Value="#FF005A9E"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>

                                <!-- Style Triggers -->
                                <Style.Triggers>
                                    <!-- Estado quando conectando - PRIORIDADE MÁXIMA -->
                                    <DataTrigger Binding="{Binding IsConnecting}" Value="True">
                                        <Setter Property="Background" Value="#FFFF9500"/>
                                        <Setter Property="Content" Value="..."/>
                                        <Setter Property="IsEnabled" Value="False"/>
                                        <Setter Property="Width" Value="50"/>
                                    </DataTrigger>

                                    <!-- Estado quando conectado - VERMELHO -->
                                    <DataTrigger Binding="{Binding IsConnected}" Value="True">
                                        <Setter Property="Background" Value="#FFDC3545"/>
                                        <Setter Property="Content" Value="Disconnect"/>
                                        <Setter Property="Width" Value="80"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}"
                                                           CornerRadius="3"
                                                           Padding="4,2">
                                                        <Border.Effect>
                                                            <DropShadowEffect Color="Transparent"
                                                                             BlurRadius="0"
                                                                             ShadowDepth="0"
                                                                             Opacity="0"/>
                                                        </Border.Effect>
                                                        <ContentPresenter HorizontalAlignment="Center" 
                                                                        VerticalAlignment="Center"/>
                                                    </Border>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Background" Value="#FFFF4757"/>
                                                            <Setter Property="Effect" Value="{DynamicResource RedShadowEffect}"/>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed" Value="True">
                                                            <Setter Property="Background" Value="#FFC82333"/>
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>

            <!-- Account ID com ícone azul - MAIS PRÓXIMO -->
            <StackPanel Grid.Row="1" 
                       Orientation="Horizontal" 
                       Margin="0,-28,0,0">
                <TextBlock Text="👤 "
                          FontSize="16"
                          Margin="0,-12,8,0"
                          VerticalAlignment="Center"
                          Foreground="#FF0078D4"/>

                <TextBlock Text="{Binding AccountCode, FallbackValue='Not connected'}" 
                          FontSize="14"
                          FontWeight="Medium"
                          Foreground="White"/>
            </StackPanel>

            <!-- Account Type & Balance - MAIS PRÓXIMO -->
            <Grid Grid.Row="2" Margin="0,-8,0,0">
                <Grid.Style>
                    <Style TargetType="Grid">
                        <Setter Property="Visibility" Value="Collapsed"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding HasAccountInfo}" Value="True">
                                <Setter Property="Visibility" Value="Visible"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Grid.Style>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Type & Balance alinhados verticalmente -->
                <Grid Grid.ColumnSpan="2" Margin="0,8,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0" VerticalAlignment="Center">
                        <TextBlock Text="Type" 
                                  FontSize="14"
                                  FontWeight="SemiBold"
                                  Foreground="#FF888888"
                                  Margin="0,0,0,0"/>
                        <Border CornerRadius="4"
                                Padding="6,2"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                Margin="0,8,0,0">
                            <Border.Style>
                                <Style TargetType="Border">
                                    <Setter Property="Background" Value="Orange"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding AccountType}" Value="Real">
                                            <Setter Property="Background" Value="Green"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Border.Style>
                            <TextBlock Text="{Binding AccountType}" 
                                      FontSize="13"
                                      FontWeight="Bold"
                                      Foreground="White"
                                      VerticalAlignment="Center"/>
                        </Border>
                    </StackPanel>
                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                        <TextBlock Text="Balance" 
                                  FontSize="14"
                                  FontWeight="SemiBold"
                                  Foreground="#FF888888"
                                  Margin="0,0,0,0"/>
                        <TextBlock FontSize="20"
                                  FontWeight="Bold"
                                  Foreground="#00D4AA"
                                  TextWrapping="Wrap"
                                  VerticalAlignment="Center">
                            <TextBlock.Text>
                                <MultiBinding StringFormat="{}{0:N2} {1}">
                                    <Binding Path="Balance"/>
                                    <Binding Path="Currency"/>
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                    </StackPanel>
                </Grid>
            </Grid>
        </Grid>
    </Border>
</UserControl>