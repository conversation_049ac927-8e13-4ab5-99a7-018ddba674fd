<UserControl x:Class="Excalibur.Views.PurchaseView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:Excalibur.Converters"
             mc:Ignorable="d"
             d:DesignHeight="360" d:DesignWidth="480">
    
    <UserControl.Resources>
        <converters:ProfitLossToColorConverter x:Key="ProfitLossToColorConverter"/>
        
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#FFCCCCCC"/>
            <Setter Property="Padding" Value="4,6"/>
            <Setter Property="TextAlignment" Value="Center"/>
        </Style>
        
        <Style x:Key="CellTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="10"/>
            <Setter Property="Foreground" Value="#FFAAAAAA"/>
            <Setter Property="Padding" Value="4,4"/>
            <Setter Property="TextAlignment" Value="Center"/>
        </Style>
    </UserControl.Resources>
    
    <Grid Margin="12,8">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Título e Status -->
        <StackPanel Grid.Row="0" Margin="0,0,0,8">
            <TextBlock Text="Compras"
                       Foreground="#FFCCCCCC"
                       FontSize="14"
                       FontWeight="SemiBold"/>
            <TextBlock FontSize="11"
                       FontStyle="Italic"
                       Margin="0,2,0,0">
                <TextBlock.Inlines>
                    <Run Text="Entradas: " Foreground="#FF888888"/>
                    <Run Text="{Binding TotalPurchaseEntries, Mode=OneWay, StringFormat={}}" Foreground="#FFCCCCCC"/>
                    <Run Text=", Lucro: " Foreground="#FF888888"/>
                    <Run Text="{Binding TotalProfitLoss, Mode=OneWay, StringFormat=F2}" Foreground="{Binding TotalProfitLoss, Converter={StaticResource ProfitLossToColorConverter}}"/>
                </TextBlock.Inlines>
            </TextBlock>
        </StackPanel>
        
        <!-- Tabela de Transações -->
        <Border Grid.Row="1" Background="#FF3F3F46" 
                BorderBrush="#FF404040" 
                BorderThickness="1" 
                CornerRadius="4">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- Cabeçalho da Tabela -->
                <Border Grid.Row="0" Background="#FF2D2D30" 
                        BorderBrush="#FF404040" 
                        BorderThickness="0,0,0,1">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="35"/>
                            <ColumnDefinition Width="70"/>
                            <ColumnDefinition Width="70"/>
                            <ColumnDefinition Width="60"/>
                            <ColumnDefinition Width="60"/>
                            <ColumnDefinition Width="60"/>
                            <ColumnDefinition Width="60"/>
                            <ColumnDefinition Width="50"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="Id" Style="{StaticResource HeaderTextStyle}"/>
                        <TextBlock Grid.Column="1" Text="Type" Style="{StaticResource HeaderTextStyle}"/>
                        <TextBlock Grid.Column="2" Text="Ref.Id" Style="{StaticResource HeaderTextStyle}"/>
                        <TextBlock Grid.Column="3" Text="Buy Time" Style="{StaticResource HeaderTextStyle}"/>
                        <TextBlock Grid.Column="4" Text="Sell Time" Style="{StaticResource HeaderTextStyle}"/>
                        <TextBlock Grid.Column="5" Text="Start Spot" Style="{StaticResource HeaderTextStyle}"/>
                        <TextBlock Grid.Column="6" Text="End Spot" Style="{StaticResource HeaderTextStyle}"/>
                        <TextBlock Grid.Column="7" Text="Stake" Style="{StaticResource HeaderTextStyle}"/>
                        <TextBlock Grid.Column="8" Text="Total P/L" Style="{StaticResource HeaderTextStyle}"/>
                        
                        <!-- Linhas verticais do cabeçalho -->
                        <Rectangle Grid.Column="0" Width="1" Fill="#FF404040" HorizontalAlignment="Right"/>
                        <Rectangle Grid.Column="1" Width="1" Fill="#FF404040" HorizontalAlignment="Right"/>
                        <Rectangle Grid.Column="2" Width="1" Fill="#FF404040" HorizontalAlignment="Right"/>
                        <Rectangle Grid.Column="3" Width="1" Fill="#FF404040" HorizontalAlignment="Right"/>
                        <Rectangle Grid.Column="4" Width="1" Fill="#FF404040" HorizontalAlignment="Right"/>
                        <Rectangle Grid.Column="5" Width="1" Fill="#FF404040" HorizontalAlignment="Right"/>
                        <Rectangle Grid.Column="6" Width="1" Fill="#FF404040" HorizontalAlignment="Right"/>
                        <Rectangle Grid.Column="7" Width="1" Fill="#FF404040" HorizontalAlignment="Right"/>
                    </Grid>
                </Border>
                
                <!-- Dados da Tabela -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" 
                              HorizontalScrollBarVisibility="Disabled">
                    <ItemsControl ItemsSource="{Binding PurchaseTransactions}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border BorderBrush="#FF404040" 
                                        BorderThickness="0,0,0,1"
                                        Background="Transparent">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="35"/>
                                            <ColumnDefinition Width="70"/>
                                            <ColumnDefinition Width="70"/>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="50"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" Text="{Binding Id}" Style="{StaticResource CellTextStyle}"/>
                                        <TextBlock Grid.Column="1" Text="{Binding Type}" Style="{StaticResource CellTextStyle}" 
                                                   ToolTip="{Binding Type}"/>
                                        <TextBlock Grid.Column="2" Text="{Binding RefId}" Style="{StaticResource CellTextStyle}"
                                                   ToolTip="{Binding RefId}"/>
                                        <TextBlock Grid.Column="3" Text="{Binding BuyTimeString}" Style="{StaticResource CellTextStyle}"/>
                                        <TextBlock Grid.Column="4" Text="{Binding SellTimeString}" Style="{StaticResource CellTextStyle}"/>
                                        <TextBlock Grid.Column="5" Text="{Binding StartSpotString}" Style="{StaticResource CellTextStyle}"/>
                                        <TextBlock Grid.Column="6" Text="{Binding EndSpotString}" Style="{StaticResource CellTextStyle}"/>
                                        <TextBlock Grid.Column="7" Text="{Binding StakeString}" Style="{StaticResource CellTextStyle}"/>
                                        <TextBlock Grid.Column="8" Text="{Binding TotalProfitLossString}" 
                                                   Style="{StaticResource CellTextStyle}"
                                                   Foreground="{Binding TotalProfitLoss, Converter={StaticResource ProfitLossToColorConverter}}"/>
                                        
                                        <!-- Linhas verticais -->
                                        <Rectangle Grid.Column="0" Width="1" Fill="#FF404040" HorizontalAlignment="Right"/>
                                        <Rectangle Grid.Column="1" Width="1" Fill="#FF404040" HorizontalAlignment="Right"/>
                                        <Rectangle Grid.Column="2" Width="1" Fill="#FF404040" HorizontalAlignment="Right"/>
                                        <Rectangle Grid.Column="3" Width="1" Fill="#FF404040" HorizontalAlignment="Right"/>
                                        <Rectangle Grid.Column="4" Width="1" Fill="#FF404040" HorizontalAlignment="Right"/>
                                        <Rectangle Grid.Column="5" Width="1" Fill="#FF404040" HorizontalAlignment="Right"/>
                                        <Rectangle Grid.Column="6" Width="1" Fill="#FF404040" HorizontalAlignment="Right"/>
                                        <Rectangle Grid.Column="7" Width="1" Fill="#FF404040" HorizontalAlignment="Right"/>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </Grid>
        </Border>
    </Grid>
</UserControl> 