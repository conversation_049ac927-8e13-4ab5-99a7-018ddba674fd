{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"Excalibur": {"commandName": "Project", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development", "DERIV_API_URL": "wss://ws.derivws.com/websockets/v3", "EXCALIBUR_LOG_LEVEL": "Debug", "EXCALIBUR_DEBUG_MODE": "true"}, "nativeDebugging": false, "sqlDebugging": false}, "Excalibur (Production-like)": {"commandName": "Project", "environmentVariables": {"DOTNET_ENVIRONMENT": "Production", "DERIV_API_URL": "wss://ws.derivws.com/websockets/v3", "EXCALIBUR_LOG_LEVEL": "Information", "EXCALIBUR_DEBUG_MODE": "false"}}}}