using System.Windows.Controls;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Input;

namespace Excalibur.Views;

public partial class MoneyManagementView : UserControl
{
    public MoneyManagementView()
    {
        InitializeComponent();

        // Restringir entrada do campo Factor a número inteiro ou decimal com até 2 casas
        FactorTextBox.PreviewTextInput += Number_PreviewTextInput;
        DataObject.AddPastingHandler(FactorTextBox, Number_Paste);
    }

    // Expressão regular: dígitos, opcional separador decimal (ponto ou vírgula) com até duas casas
    private static readonly Regex _decimalRegex = new("^\\d*(?:[.,]\\d{0,2})?$");

    private void Number_PreviewTextInput(object sender, TextCompositionEventArgs e)
    {
        if (sender is TextBox tb)
        {
            string newText = GetProposedText(tb, e.Text);
            e.Handled = !_decimalRegex.IsMatch(newText);
        }
    }

    private void Number_Paste(object sender, DataObjectPastingEventArgs e)
    {
        if (e.DataObject.GetDataPresent(DataFormats.Text))
        {
            string pasteText = e.DataObject.GetData(DataFormats.Text) as string ?? string.Empty;
            if (sender is TextBox tb)
            {
                string newText = GetProposedText(tb, pasteText);
                if (!_decimalRegex.IsMatch(newText))
                {
                    e.CancelCommand();
                }
            }
        }
        else
        {
            e.CancelCommand();
        }
    }

    private static string GetProposedText(TextBox textBox, string input)
    {
        var current = textBox.Text ?? string.Empty;
        int selectionStart = textBox.SelectionStart;
        int selectionLength = textBox.SelectionLength;
        string newText = current.Remove(selectionStart, selectionLength).Insert(selectionStart, input);
        return newText;
    }
} 