using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Excalibur.Models;

public class MartingaleGroup : INotifyPropertyChanged
{
    private string _id = string.Empty;
    private int _currentLevel = 1;
    private decimal _baseStake = 0;
    private decimal _factor = 2;
    private int _maxLevel = 1;
    private bool _isActive = true;
    private DateTime _createdAt = DateTime.Now;

    public string Id
    {
        get => _id;
        set
        {
            _id = value;
            OnPropertyChanged();
        }
    }

    public int CurrentLevel
    {
        get => _currentLevel;
        set
        {
            _currentLevel = value;
            OnPropertyChanged();
        }
    }

    public decimal BaseStake
    {
        get => _baseStake;
        set
        {
            _baseStake = value;
            OnPropertyChanged();
        }
    }

    public decimal Factor
    {
        get => _factor;
        set
        {
            _factor = value;
            OnPropertyChanged();
        }
    }

    public int MaxLevel
    {
        get => _maxLevel;
        set
        {
            _maxLevel = value;
            OnPropertyChanged();
        }
    }

    public bool IsActive
    {
        get => _isActive;
        set
        {
            _isActive = value;
            OnPropertyChanged();
        }
    }

    public DateTime CreatedAt
    {
        get => _createdAt;
        set
        {
            _createdAt = value;
            OnPropertyChanged();
        }
    }

    public decimal CalculateCurrentStake()
    {
        return BaseStake * (decimal)Math.Pow((double)Factor, CurrentLevel - 1);
    }

    public void IncrementLevel()
    {
        if (CurrentLevel < MaxLevel)
        {
            CurrentLevel++;
        }
    }

    public void Reset()
    {
        CurrentLevel = 1;
    }

    public bool HasReachedMaxLevel()
    {
        return CurrentLevel >= MaxLevel;
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}