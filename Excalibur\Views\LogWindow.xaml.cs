using System.Windows;
using Excalibur.ViewModels;

namespace Excalibur.Views;

public partial class LogWindow : Window
{
    public LogWindow(LogViewModel vm)
    {
        InitializeComponent();
        DataContext = vm;
    }

    public void CopyAll_Click(object sender, RoutedEventArgs e)
    {
        if (DataContext is LogViewModel vm)
        {
            var text = string.Join("\n", vm.Messages);
            Clipboard.SetText(text);
        }
    }
} 