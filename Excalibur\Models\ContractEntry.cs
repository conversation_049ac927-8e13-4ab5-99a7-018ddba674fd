using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Excalibur.Models;

public class ContractEntry : INotifyPropertyChanged
{
    private int _id;
    private string _type = string.Empty;
    private string _refId = string.Empty;
    private DateTime? _buyTime;
    private DateTime? _sellTime;
    private decimal _startSpot;
    private decimal _endSpot;
    private decimal _stake;
    private decimal _totalProfitLoss;
    private bool _isActive = true;
    private TimeSpan _duration = TimeSpan.FromMinutes(1);

    public int Id
    {
        get => _id;
        set
        {
            _id = value;
            OnPropertyChanged();
        }
    }

    public string Type
    {
        get => _type;
        set
        {
            _type = value;
            OnPropertyChanged();
        }
    }

    public string RefId
    {
        get => _refId;
        set
        {
            _refId = value;
            OnPropertyChanged();
        }
    }

    public DateTime? BuyTime
    {
        get => _buyTime;
        set
        {
            _buyTime = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(BuyTimeString));
        }
    }

    public DateTime? SellTime
    {
        get => _sellTime;
        set
        {
            _sellTime = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(SellTimeString));
        }
    }

    public decimal StartSpot
    {
        get => _startSpot;
        set
        {
            _startSpot = value;
            OnPropertyChanged();
            UpdateProfitLoss();
        }
    }

    public decimal EndSpot
    {
        get => _endSpot;
        set
        {
            _endSpot = value;
            OnPropertyChanged();
            UpdateProfitLoss();
        }
    }

    public decimal Stake
    {
        get => _stake;
        set
        {
            _stake = value;
            OnPropertyChanged();
            UpdateProfitLoss();
        }
    }

    public decimal TotalProfitLoss
    {
        get => _totalProfitLoss;
        set
        {
            _totalProfitLoss = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(ProfitLossColor));
        }
    }

    public bool IsActive
    {
        get => _isActive;
        set
        {
            _isActive = value;
            OnPropertyChanged();
        }
    }

    public TimeSpan Duration
    {
        get => _duration;
        set
        {
            _duration = value;
            OnPropertyChanged();
        }
    }

    public string BuyTimeString => BuyTime?.ToString("HH:mm:ss") ?? "-";
    public string SellTimeString => SellTime?.ToString("HH:mm:ss") ?? "-";
    public string ProfitLossColor => TotalProfitLoss >= 0 ? "#FF4CAF50" : "#FFF44336";

    public bool IsExpired => BuyTime.HasValue && DateTime.Now - BuyTime.Value > Duration;

    private void UpdateProfitLoss()
    {
        if (IsActive && EndSpot > 0)
        {
            if (IsExpired && SellTime == null)
            {
                SellTime = DateTime.Now;
                IsActive = false;
            }
            
            decimal difference = EndSpot - StartSpot;
            TotalProfitLoss = difference * Stake;
        }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}