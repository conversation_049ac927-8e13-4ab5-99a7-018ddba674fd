using System;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;

namespace Excalibur.Models;

public class ContractTransaction : INotifyPropertyChanged
{
    private int _id;
    private string _type = string.Empty;
    private string _refId = string.Empty;
    private DateTime _buyTime;
    private DateTime? _sellTime;
    private decimal _startSpot;
    private decimal _endSpot;
    private decimal _stake;
    private decimal _payout;
    private decimal _totalProfitLoss;
    private bool _isActive = true;
    private int _duration;
    private string _durationType = string.Empty;
    private bool _profitFromApi = false;

    public int Id
    {
        get => _id;
        set
        {
            _id = value;
            OnPropertyChanged();
        }
    }

    public string Type
    {
        get => _type;
        set
        {
            _type = value;
            OnPropertyChanged();
        }
    }

    public string RefId
    {
        get => _refId;
        set
        {
            _refId = value;
            OnPropertyChanged();
        }
    }

    public DateTime BuyTime
    {
        get => _buyTime;
        set
        {
            _buyTime = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(BuyTimeString));
        }
    }

    public DateTime? SellTime
    {
        get => _sellTime;
        set
        {
            _sellTime = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(SellTimeString));
        }
    }

    public decimal StartSpot
    {
        get => _startSpot;
        set
        {
            _startSpot = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(StartSpotString));
            OnPropertyChanged(nameof(CurrentPriceVariationString));
        }
    }

    public decimal EndSpot
    {
        get => _endSpot;
        set
        {
            _endSpot = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(EndSpotString));
            OnPropertyChanged(nameof(CurrentPriceVariationString));
        }
    }

    public decimal Stake
    {
        get => _stake;
        set
        {
            _stake = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(StakeString));
        }
    }

    public decimal Payout
    {
        get => _payout;
        set
        {
            _payout = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(PayoutString));
        }
    }

    public decimal TotalProfitLoss
    {
        get => _totalProfitLoss;
        set
        {
            _totalProfitLoss = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(TotalProfitLossString));
        }
    }

    public bool IsActive
    {
        get => _isActive;
        set
        {
            _isActive = value;
            OnPropertyChanged();
        }
    }

    public int Duration
    {
        get => _duration;
        set
        {
            _duration = value;
            OnPropertyChanged();
        }
    }

    public string DurationType
    {
        get => _durationType;
        set
        {
            _durationType = value;
            OnPropertyChanged();
        }
    }
    
    public bool ProfitFromApi
    {
        get => _profitFromApi;
        set
        {
            _profitFromApi = value;
            OnPropertyChanged();
        }
    }

    // Propriedades formatadas para display
    public string BuyTimeString => BuyTime.ToString("HH:mm:ss");
    public string SellTimeString => SellTime?.ToString("HH:mm:ss") ?? "-";
    public string StartSpotString => StartSpot.ToString("F3");
    public string EndSpotString => EndSpot.ToString("F3");
    public string StakeString => Stake.ToString("F2");
    public string PayoutString => Payout.ToString("F2");
    public string TotalProfitLossString => TotalProfitLoss.ToString("F2");
    
    // Propriedade para mostrar a variação de preço atual
    public string CurrentPriceVariationString 
    { 
        get
        {
            if (StartSpot == 0) return "0.000";
            var variation = EndSpot - StartSpot;
            return variation.ToString("F3");
        }
    }

    // Calcular tempo de expiração em segundos
    public double GetExpirationTimeInSeconds()
    {
        return DurationType.ToLower() switch
        {
            "t" => Duration * 2, // ticks: cada tick = 2 segundos
            "s" => Duration, // segundos
            "m" => Duration * 60, // minutos
            "h" => Duration * 3600, // horas
            "d" => Duration * 86400, // dias
            _ => 30 // fallback padrão
        };
    }

    // Verificar se o contrato está ganhando no momento
    public bool IsCurrentlyWinning(decimal currentSpot)
    {
        // Se o contrato já expirou, usar o EndSpot para determinar resultado
        var spotToCompare = IsActive ? currentSpot : EndSpot;
        
        try
        {
            // Log detalhado para diagnóstico
            System.Diagnostics.Debug.WriteLine($"VERIFICANDO VITÓRIA: RefId={RefId}, Tipo={Type}, StartSpot={StartSpot}, CurrentSpot={spotToCompare}");
            
            // Padronizar o texto do tipo para comparação mais confiável
            var lowerType = Type.ToLowerInvariant();

            // Contratos de descida (Put/Down/Lower/Fall)
            if (lowerType.Contains("lower") || lowerType.Contains("fall") || 
                lowerType.Contains("down") || lowerType.Contains("put"))
            {
                var result = spotToCompare < StartSpot;
                var statusText = result ? "GANHANDO" : "PERDENDO";
                System.Diagnostics.Debug.WriteLine($"É contrato de DESCIDA: {statusText} - Spot: {spotToCompare} < {StartSpot} = {result}");
                return result; // Ganha se spot final < spot inicial
            }
            
            // Contratos de subida (Call/Up/Higher/Rise)
            else if (lowerType.Contains("higher") || lowerType.Contains("rise") || 
                     lowerType.Contains("up") || lowerType.Contains("call"))
            {
                var result = spotToCompare > StartSpot;
                var statusText = result ? "GANHANDO" : "PERDENDO";
                System.Diagnostics.Debug.WriteLine($"É contrato de SUBIDA: {statusText} - Spot: {spotToCompare} > {StartSpot} = {result}");
                return result; // Ganha se spot final > spot inicial
            }
            
            // Contratos de igualdade (Matches/Equals)
            else if (lowerType.Contains("matches") || lowerType.Contains("equals"))
            {
                // Para contratos de igual, verificar se o último dígito é igual
                var startDigit = GetLastDigit(StartSpot);
                var endDigit = GetLastDigit(spotToCompare);
                var result = startDigit == endDigit;
                var statusText = result ? "GANHANDO" : "PERDENDO";
                System.Diagnostics.Debug.WriteLine($"É contrato de IGUALDADE: {statusText} - Dígitos: {startDigit} == {endDigit} = {result}");
                return result;
            }
            
            // Contratos de diferença (Differs/NotEqual)
            else if (lowerType.Contains("differs") || lowerType.Contains("not equal"))
            {
                // Para contratos de diferente, verificar se o último dígito é diferente
                var startDigit = GetLastDigit(StartSpot);
                var endDigit = GetLastDigit(spotToCompare);
                var result = startDigit != endDigit;
                var statusText = result ? "GANHANDO" : "PERDENDO";
                System.Diagnostics.Debug.WriteLine($"É contrato de DIFERENÇA: {statusText} - Dígitos: {startDigit} != {endDigit} = {result}");
                return result;
            }
            
            // Tipo desconhecido - logar e usar regra padrão
            System.Diagnostics.Debug.WriteLine($"TIPO DESCONHECIDO: {Type} - assumindo HIGHER");
            return spotToCompare > StartSpot; // Assumir higher como padrão
        }
        catch (Exception ex) 
        {
            System.Diagnostics.Debug.WriteLine($"ERRO ao verificar vitória: {ex.Message}");
            return false; // Em caso de erro, assumir perdendo
        }
    }
    
    private int GetLastDigit(decimal value)
    {
        // Obter o último dígito do valor
        var stringValue = value.ToString("F5");
        var lastChar = stringValue.Replace(".", "").Replace(",", "").Last();
        return int.Parse(lastChar.ToString());
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    public virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
} 