﻿using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Excalibur.Core.Interfaces;
using Excalibur.Core.Models.DTOs.ApiResponses;
using System.Configuration;

namespace Excalibur.ViewModels;

public class AccountInfoViewModel : INotifyPropertyChanged, IDisposable
{
    private readonly IDerivApiService _derivApiService;

    // Campos privados
    private string _accountCode = "Not connected";
    private string _accountType = string.Empty;
    private decimal _balance = 0m;
    private string _currency = "USD";
    private bool _isConnected = false;
    private bool _isConnecting = false;
    private string _connectionStatus = "Offline";
    private bool _hasAccountInfo = false;
    private string _lastErrorMessage = string.Empty;
    private bool _disposed = false;

    // Propriedades públicas
    public string AccountCode
    {
        get => _accountCode;
        set
        {
            _accountCode = value;
            OnPropertyChanged();
        }
    }

    public string AccountType
    {
        get => _accountType;
        set
        {
            _accountType = value;
            OnPropertyChanged();
        }
    }

    public decimal Balance
    {
        get => _balance;
        set
        {
            _balance = value;
            OnPropertyChanged();
        }
    }

    public string Currency
    {
        get => _currency;
        set
        {
            _currency = value;
            OnPropertyChanged();
        }
    }

    public bool IsConnected
    {
        get => _isConnected;
        set
        {
            _isConnected = value;
            OnPropertyChanged();
        }
    }

    public bool IsConnecting
    {
        get => _isConnecting;
        set
        {
            _isConnecting = value;
            OnPropertyChanged();
        }
    }

    public string ConnectionStatus
    {
        get => _connectionStatus;
        set
        {
            _connectionStatus = value;
            OnPropertyChanged();
        }
    }

    public bool HasAccountInfo
    {
        get => _hasAccountInfo;
        set
        {
            _hasAccountInfo = value;
            OnPropertyChanged();
        }
    }

    public string LastErrorMessage
    {
        get => _lastErrorMessage;
        set
        {
            _lastErrorMessage = value;
            OnPropertyChanged();
        }
    }

    // Comando
    public ICommand ConnectCommand { get; }

    // Construtor
    public AccountInfoViewModel(IDerivApiService derivApiService)
    {
        _derivApiService = derivApiService;

        // Subscrever eventos
        _derivApiService.AccountInfoReceived += OnAccountInfoReceived;
        _derivApiService.ConnectionStateChanged += OnConnectionStateChanged;
        _derivApiService.ErrorOccurred += OnErrorOccurred;
        _derivApiService.BuyCompleted += OnBuyCompleted;
        _derivApiService.BalanceUpdated += OnBalanceUpdated;

        // Criar comando
        ConnectCommand = new AsyncCommand(ExecuteConnectCommand);
    }

    // Método do comando
    private async Task ExecuteConnectCommand()
    {
        if (IsConnected)
        {
            await DisconnectAsync();
        }
        else
        {
            await ConnectAsync();
        }
    }

    private async Task ConnectAsync()
    {
        IsConnecting = true;
        ConnectionStatus = "Connecting...";

        try
        {
            var connected = await _derivApiService.ConnectAsync();

            if (connected)
            {
                // Assim que conectar, já autentica
                ConnectionStatus = "Authenticating...";
                string apiKey = ConfigurationManager.AppSettings["DerivApiKey"] ?? Environment.GetEnvironmentVariable("DERIV_API_KEY") ?? "00I1wVoORm3kOYZ";
                await _derivApiService.AuthorizeAsync(apiKey);
                // Aguardaremos a resposta de autorização; quando chegar, o event handler
                // OnAccountInfoReceived definirá ConnectionStatus = "Online".
            }
        }
        catch (Exception ex)
        {
            ConnectionStatus = $"Error: {ex.Message}";
        }
        finally
        {
            IsConnecting = false;
        }
    }

    private async Task DisconnectAsync()
    {
        try
        {
            await _derivApiService.DisconnectAsync();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Disconnect error: {ex.Message}");
        }
    }

    // Event handlers
    private void OnAccountInfoReceived(object? sender, AccountInfo accountInfo)
    {
        if (_disposed || accountInfo == null) return;

        // Atualizar UI na thread principal
        var dispatcher = Application.Current?.Dispatcher;
        if (dispatcher != null)
        {
            dispatcher.Invoke(() =>
            {
                try
                {
                    System.Diagnostics.Debug.WriteLine($"[DEBUG] Account info received: LoginId={accountInfo.LoginId}, Balance={accountInfo.Balance}, Currency={accountInfo.Currency}, IsVirtual={(accountInfo.IsVirtual == 1)}");
                    
                    AccountCode = accountInfo.LoginId ?? "Unknown";
                    AccountType = accountInfo.IsVirtual == 1 ? "Virtual" : "Real";
                    Balance = accountInfo.Balance;
                    Currency = accountInfo.Currency ?? "USD";
                    ConnectionStatus = "Online";
                    HasAccountInfo = true;
                    
                    // Forçar atualização das propriedades
                    OnPropertyChanged(nameof(AccountCode));
                    OnPropertyChanged(nameof(AccountType));
                    OnPropertyChanged(nameof(Balance));
                    OnPropertyChanged(nameof(Currency));
                    OnPropertyChanged(nameof(ConnectionStatus));
                    
                    System.Diagnostics.Debug.WriteLine($"[DEBUG] Properties updated: AccountCode={AccountCode}, Balance={Balance}, Currency={Currency}");
                    
                    // Após receber informações da conta, solicitar símbolos ativos
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            if (!_disposed)
                            {
                                await _derivApiService.RequestActiveSymbolsAsync();
                                System.Diagnostics.Debug.WriteLine("[DEBUG] Active symbols requested");
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"[ERROR] Erro ao solicitar símbolos ativos: {ex.Message}");
                        }
                    });
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[ERROR] Erro ao processar dados da conta: {ex.Message}");
                    LastErrorMessage = $"Erro ao processar dados da conta: {ex.Message}";
                }
            });
        }
    }

    private void OnConnectionStateChanged(object? sender, bool isConnected)
    {
        if (_disposed) return;

        var dispatcher = Application.Current?.Dispatcher;
        if (dispatcher != null)
        {
            dispatcher.Invoke(() =>
            {
                IsConnected = isConnected;

                if (isConnected)
                {
                    ConnectionStatus = "Connected";
                }
                else
                {
                    ConnectionStatus = "Offline";
                    AccountCode = "Not connected";
                    Balance = 0m;
                    Currency = "USD";
                    AccountType = string.Empty;
                    HasAccountInfo = false;
                    
                    // Forçar atualização das propriedades
                    OnPropertyChanged(nameof(AccountCode));
                    OnPropertyChanged(nameof(Balance));
                    OnPropertyChanged(nameof(Currency));
                    OnPropertyChanged(nameof(AccountType));
                    OnPropertyChanged(nameof(ConnectionStatus));
                }
            });
        }
    }

    private void OnErrorOccurred(object? sender, string error)
    {
        if (_disposed) return;

        // Salvar mensagem de erro
        LastErrorMessage = error ?? "Unknown error";

        // Se a conexão realmente caiu, atualizar status
        if (_derivApiService?.IsConnected == false)
        {
            ConnectionStatus = "Error";
        }

        IsConnecting = false;
    }

    private void OnBuyCompleted(object? sender, string message)
    {
        if (_disposed) return;

        var dispatcher = Application.Current?.Dispatcher;
        if (dispatcher != null)
        {
            dispatcher.Invoke(() =>
            {
                System.Diagnostics.Debug.WriteLine($"[ACCOUNT] Buy completed: {message}");
            });
        }
    }

    private void OnBalanceUpdated(object? sender, decimal newBalance)
    {
        if (_disposed) return;

        var dispatcher = Application.Current?.Dispatcher;
        if (dispatcher != null)
        {
            dispatcher.Invoke(() =>
            {
                System.Diagnostics.Debug.WriteLine($"[ACCOUNT] Balance updated: {Balance:C2} -> {newBalance:C2}");
                Balance = newBalance;
            });
        }
    }

    // INotifyPropertyChanged
    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        if (!_disposed)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // IDisposable
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;

            // Unsubscribe from events
            if (_derivApiService != null)
            {
                _derivApiService.AccountInfoReceived -= OnAccountInfoReceived;
                _derivApiService.ConnectionStateChanged -= OnConnectionStateChanged;
                _derivApiService.ErrorOccurred -= OnErrorOccurred;
                _derivApiService.BuyCompleted -= OnBuyCompleted;
                _derivApiService.BalanceUpdated -= OnBalanceUpdated;
            }

            GC.SuppressFinalize(this);
        }
    }
}