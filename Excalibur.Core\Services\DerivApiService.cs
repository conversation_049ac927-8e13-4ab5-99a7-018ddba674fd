﻿using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.IO;
using Excalibur.Core.Interfaces;
using Excalibur.Core.Models.DTOs.ApiResponses;
using Excalibur.Core.Models.DTOs.ApiRequests;
using Microsoft.Extensions.Logging;

namespace Excalibur.Core.Services;

public class DerivApiService : IDerivApiService, IDisposable
{
    private const string WebSocketUrl = "wss://ws.derivws.com/websockets/v3?app_id={0}";
    private readonly ClientWebSocket _webSocket;
    private readonly CancellationTokenSource _cancellationTokenSource;
    private readonly ILogger<DerivApiService> _logger;
    private int _requestId;
    private Timer? _pingTimer;
    
    // Rate limiting para proposal_open_contract
    private readonly Dictionary<string, DateTime> _lastContractRequest = new();
    private readonly SemaphoreSlim _contractRequestSemaphore = new(1, 1);
    private const int CONTRACT_REQUEST_THROTTLE_SECONDS = 3;
    
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNameCaseInsensitive = true,
        NumberHandling = JsonNumberHandling.AllowReadingFromString,
        DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
    };

    public string AppId { get; private set; }
    public string? Token { get; private set; }
    public bool IsConnected => _webSocket.State == WebSocketState.Open;

    // Eventos
    public event EventHandler<AccountInfo>? AccountInfoReceived;
    public event EventHandler<string>? ErrorOccurred;
    public event EventHandler<bool>? ConnectionStateChanged;
    public event EventHandler<List<ActiveSymbol>>? ActiveSymbolsReceived;
    public event EventHandler<(string Symbol, List<ContractType> Contracts)>? ContractsForReceived;
    public event EventHandler<ProposalResponse>? ProposalReceived;
    public event EventHandler<TickData>? TickReceived;
    public event EventHandler<string>? BuyCompleted;
    public event EventHandler<decimal>? BalanceUpdated;
    public event EventHandler<ProfitTableTransaction>? ProfitTableReceived;
    public event EventHandler<OpenContractDetails>? OpenContractReceived;

    public DerivApiService (ILogger<DerivApiService> logger)
    {
        _logger = logger;
        AppId = "82663"; // Seu APP ID
        _webSocket = new ClientWebSocket();
        _cancellationTokenSource = new CancellationTokenSource();
        _requestId = 1;
    }

    public async Task<bool> ConnectAsync ()
    {
        try
        {
            var uri = new Uri(string.Format(WebSocketUrl, AppId));
            await _webSocket.ConnectAsync(uri, _cancellationTokenSource.Token);

            // Iniciar listener de mensagens
            _ = Task.Run(StartListening, _cancellationTokenSource.Token);

            // Iniciar timer de ping (120 segundos)
            _pingTimer = new Timer(SendPing, null, 120000, 120000);

            _logger.LogInformation("Conectado ao WebSocket da Deriv");
            ConnectionStateChanged?.Invoke(this, true);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao conectar ao WebSocket");
            ErrorOccurred?.Invoke(this, $"Erro ao conectar: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> AuthorizeAsync (string token)
    {
        Token = token;
        var request = new AuthorizeRequest {
            Authorize = token,
            ReqId = GetNextRequestId()
        };

        await SendRequestAsync(request);
        return true;
    }

    public async Task<AccountInfo?> GetAccountInfoAsync ()
    {
        // A informação da conta vem automaticamente após autorização
        // Por agora, retornamos null e aguardamos o evento
        return null;
    }

    public int GetNextRequestId () => Interlocked.Increment(ref _requestId);

    public async Task RequestActiveSymbolsAsync()
    {
        var request = new
        {
            active_symbols = "brief",
            req_id = GetNextRequestId()
        };

        await SendRequestAsync(request);
    }

    public async Task RequestContractsForAsync(string symbol)
    {
        var request = new
        {
            contracts_for = symbol,
            req_id = GetNextRequestId()
        };

        await SendRequestAsync(request);
    }

    public async Task RequestProposalAsync(string symbol, string contractType, decimal amount, int duration, string durationUnit, string? barrier = null)
    {
        var request = new ProposalRequest
        {
            Symbol = symbol,
            ContractType = contractType,
            Amount = amount,
            Duration = duration,
            DurationUnit = durationUnit,
            Barrier = barrier,
            ReqId = GetNextRequestId(),
            Basis = "stake"
        };

        await SendRequestAsync(request);
    }

    public async Task RequestTicksStreamAsync(string symbol)
    {
        var request = new
        {
            ticks = symbol,
            subscribe = 1,
            req_id = GetNextRequestId()
        };

        await SendRequestAsync(request);
    }

    public async Task BuyContractAsync(string proposalId, decimal price)
    {
        var request = new
        {
            buy = proposalId,
            price = price,
            req_id = GetNextRequestId()
        };

        await SendRequestAsync(request);
    }

    public async Task RequestProfitTableAsync()
    {
        var request = new
        {
            profit_table = 1,
            req_id = GetNextRequestId()
        };

        await SendRequestAsync(request);
    }

    public async Task RequestContractDetailsAsync(string contractId)
    {
        if (string.IsNullOrEmpty(contractId))
        {
            _logger.LogWarning("RequestContractDetailsAsync: contractId is null or empty");
            return;
        }

        // Rate limiting - verificar se já fizemos request recente para este contrato
        await _contractRequestSemaphore.WaitAsync();
        try
        {
            if (_lastContractRequest.TryGetValue(contractId, out var lastRequest))
            {
                var timeSinceLastRequest = DateTime.Now - lastRequest;
                if (timeSinceLastRequest.TotalSeconds < CONTRACT_REQUEST_THROTTLE_SECONDS)
                {
                    _logger.LogDebug("Throttling contract request for {ContractId}, last request was {Seconds:F1}s ago", 
                        contractId, timeSinceLastRequest.TotalSeconds);
                    return;
                }
            }

            _lastContractRequest[contractId] = DateTime.Now;
            
            var request = new
            {
                proposal_open_contract = 1,
                contract_id = contractId,
                req_id = GetNextRequestId()
            };

            _logger.LogDebug("Requesting contract details for {ContractId}", contractId);
            await SendRequestAsync(request);
        }
        finally
        {
            _contractRequestSemaphore.Release();
        }
    }

    private async Task SendRequestAsync<T> (T request)
    {
        try
        {
            var json = JsonSerializer.Serialize(request, JsonOptions);
            var bytes = Encoding.UTF8.GetBytes(json);
            var arraySegment = new ArraySegment<byte>(bytes);

            await _webSocket.SendAsync(arraySegment, WebSocketMessageType.Text, true, _cancellationTokenSource.Token);
            _logger.LogDebug("Enviado: {Json}", json);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao enviar requisição");
            ErrorOccurred?.Invoke(this, $"Erro ao enviar: {ex.Message}");
        }
    }

    private async Task StartListening()
    {
        var buffer = new byte[4096];

        while (_webSocket.State == WebSocketState.Open && !_cancellationTokenSource.Token.IsCancellationRequested)
        {
            try
            {
                using var ms = new MemoryStream();
                WebSocketReceiveResult? result;

                do
                {
                    result = await _webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), _cancellationTokenSource.Token);
                    if (result.MessageType == WebSocketMessageType.Close)
                    {
                        await _webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing", CancellationToken.None);
                        ConnectionStateChanged?.Invoke(this, false);
                        return;
                    }

                    ms.Write(buffer, 0, result.Count);

                } while (!result.EndOfMessage);

                // Converter bytes para string e processar
                var messageBytes = ms.ToArray();
                var message = Encoding.UTF8.GetString(messageBytes);
                ProcessMessage(message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao receber mensagem");
                ErrorOccurred?.Invoke(this, $"Erro de conexão: {ex.Message}");
                break;
            }
        }

        ConnectionStateChanged?.Invoke(this, false);
    }

    private void ProcessMessage (string message)
    {
        try
        {
            _logger.LogDebug("Recebido: {Message}", message);

            using var document = JsonDocument.Parse(message);
            var root = document.RootElement;

            // Verificar se é resposta de autorização com informações da conta
            if (root.TryGetProperty("authorize", out var authorizeElement))
            {
                try
                {
                    var accountInfo = JsonSerializer.Deserialize<AccountInfo>(authorizeElement.GetRawText(), JsonOptions);
                    if (accountInfo != null)
                    {
                        _logger.LogInformation("Account info received: {LoginId}, Balance: {Balance} {Currency}", 
                            accountInfo.LoginId, accountInfo.Balance, accountInfo.Currency);
                        AccountInfoReceived?.Invoke(this, accountInfo);
                    }
                }
                catch (JsonException ex)
                {
                    _logger.LogError(ex, "Erro ao deserializar AccountInfo: {Json}", authorizeElement.GetRawText());
                }
            }

            // Verificar se é resposta de símbolos ativos
            if (root.TryGetProperty("active_symbols", out var activeSymbolsElement))
            {
                var symbols = JsonSerializer.Deserialize<List<ActiveSymbol>>(activeSymbolsElement.GetRawText(), JsonOptions);
                if (symbols != null)
                {
                    _logger.LogInformation("Active symbols received: {Count} symbols", symbols.Count);
                    ActiveSymbolsReceived?.Invoke(this, symbols);
                }
            }

            // Verificar se é resposta de contratos
            if (root.TryGetProperty("contracts_for", out var contractsForElement))
            {
                if (contractsForElement.TryGetProperty("available", out var availableElement))
                {
                    var contracts = JsonSerializer.Deserialize<List<ContractType>>(availableElement.GetRawText(), JsonOptions);
                    if (contracts != null && root.TryGetProperty("echo_req", out var echoReq) && 
                        echoReq.TryGetProperty("contracts_for", out var symbolElement))
                    {
                        var symbol = symbolElement.GetString();
                        if (!string.IsNullOrEmpty(symbol))
                        {
                            _logger.LogInformation("Contracts received for {Symbol}: {Count} contracts", symbol, contracts.Count);
                            ContractsForReceived?.Invoke(this, (symbol, contracts));
                        }
                    }
                }
            }

            // Verificar se é resposta de proposta
            if (root.TryGetProperty("proposal", out var proposalElement))
            {
                try
                {
                    var proposalResponse = JsonSerializer.Deserialize<ProposalResponse>(message, JsonOptions);
                    if (proposalResponse != null)
                    {
                        _logger.LogInformation("Proposal received: Payout={Payout}, AskPrice={AskPrice}", 
                            proposalResponse.Proposal.Payout, proposalResponse.Proposal.AskPrice);
                        ProposalReceived?.Invoke(this, proposalResponse);
                    }
                }
                catch (JsonException ex)
                {
                    _logger.LogError(ex, "Erro ao deserializar ProposalResponse: {Json}", message);
                }
            }

            // Verificar se é tick stream
            if (root.TryGetProperty("tick", out var tickRoot))
            {
                var symbol = tickRoot.GetProperty("symbol").GetString();
                var price = tickRoot.GetProperty("quote").GetDecimal();
                var epoch = tickRoot.GetProperty("epoch").GetInt64();

                if (!string.IsNullOrEmpty(symbol))
                {
                    var tickData = new TickData { Symbol = symbol, Price = price, Epoch = epoch };
                    TickReceived?.Invoke(this, tickData);
                }
            }

            // Verificar se é resposta de compra
            if (root.TryGetProperty("buy", out var buyElement))
            {
                try
                {
                    var contractId = buyElement.GetProperty("contract_id").GetInt64().ToString();
                    var buyPrice = buyElement.GetProperty("buy_price").GetDecimal();
                    var payout = buyElement.GetProperty("payout").GetDecimal();
                    var balanceAfter = buyElement.GetProperty("balance_after").GetDecimal();
                    
                    _logger.LogInformation("Compra realizada com sucesso: ContractId={ContractId}, BuyPrice={BuyPrice}, Payout={Payout}, Balance={Balance}", 
                        contractId, buyPrice, payout, balanceAfter);
                    
                    BuyCompleted?.Invoke(this, $"Compra realizada! ID: {contractId}, Preço: {buyPrice:C2}, Payout: {payout:C2}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erro ao processar resposta de compra: {Json}", buyElement.GetRawText());
                    BuyCompleted?.Invoke(this, "Erro ao processar confirmação de compra");
                }
            }

            // Verificar se é resposta de profit table
            if (root.TryGetProperty("profit_table", out var profitTableElement))
            {
                try
                {
                    if (profitTableElement.TryGetProperty("transactions", out var transactionsElement))
                    {
                        var transactions = JsonSerializer.Deserialize<List<ProfitTableTransaction>>(transactionsElement.GetRawText(), JsonOptions);
                        if (transactions != null)
                        {
                            foreach (var transaction in transactions)
                            {
                                ProfitTableReceived?.Invoke(this, transaction);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erro ao processar profit table: {Json}", profitTableElement.GetRawText());
                }
            }

            // Verificar se é resposta de contract details
            if (root.TryGetProperty("proposal_open_contract", out var contractElement))
            {
                try
                {
                    var contractDetails = JsonSerializer.Deserialize<OpenContractDetails>(contractElement.GetRawText(), JsonOptions);
                    if (contractDetails != null)
                    {
                        OpenContractReceived?.Invoke(this, contractDetails);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erro ao processar contract details: {Json}", contractElement.GetRawText());
                }
            }

            // Verificar se há erro na resposta
            if (root.TryGetProperty("error", out var errorElement))
            {
                var errorMessage = errorElement.GetProperty("message").GetString();
                _logger.LogError("Erro da API: {Error}", errorMessage);
                ErrorOccurred?.Invoke(this, errorMessage ?? "Erro desconhecido");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar mensagem: {Message}", message);
        }
    }

    private void SendPing (object? state)
    {
        if (IsConnected)
        {
            var pingRequest = new PingRequest { ReqId = GetNextRequestId() };
            _ = SendRequestAsync(pingRequest);
        }
    }

    public async Task DisconnectAsync ()
    {
        _pingTimer?.Dispose();
        _cancellationTokenSource.Cancel();

        if (_webSocket.State == WebSocketState.Open)
        {
            await _webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Desconectando", CancellationToken.None);
        }
    }

    public void Dispose ()
    {
        _pingTimer?.Dispose();
        _cancellationTokenSource.Dispose();
        _webSocket.Dispose();
        GC.SuppressFinalize(this);
    }


}