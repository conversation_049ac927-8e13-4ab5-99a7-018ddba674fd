<UserControl x:Class="Excalibur.Views.MoneyManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="300" d:DesignWidth="320">
    <UserControl.Resources>
        <!-- Estilos reutilizados do ProposalView -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#FFCCCCCC"/>
            <Setter Property="Margin" Value="0,8,0,4"/>
        </Style>
        <Style x:Key="InputStyle" TargetType="TextBox">
            <Setter Property="Background" Value="#FF3F3F46"/>
            <Setter Property="BorderBrush" Value="#FF404040"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Foreground" Value="#FFCCCCCC"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost"
                                          Padding="{TemplateBinding Padding}"
                                          VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Toggle Switch Style -->
        <Style x:Key="ToggleSwitchStyle" TargetType="ToggleButton">
            <Setter Property="Width" Value="32"/>
            <Setter Property="Height" Value="18"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToggleButton">
                        <Grid>
                            <!-- Track -->
                            <Border x:Name="SwitchBorder"
                                    CornerRadius="9"
                                    Background="#FF5A5A5A"
                                    BorderBrush="#FF888888"
                                    BorderThickness="1"/>

                            <!-- Knob -->
                            <Ellipse x:Name="Knob"
                                     Width="16" Height="16"
                                     Fill="White"
                                     Margin="1"
                                     HorizontalAlignment="Left"/>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="SwitchBorder" Property="Background" Value="#FFFFC107"/>
                                <Setter TargetName="Knob" Property="HorizontalAlignment" Value="Right"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="SwitchBorder" Property="Opacity" Value="0.5"/>
                                <Setter TargetName="Knob" Property="Opacity" Value="0.5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="16,12">
            <TextBlock Text="Money Management"
                       FontSize="14" FontWeight="SemiBold"
                       Foreground="#FFCCCCCC" Margin="0,0,0,12"/>

            <!-- Martingale Section -->
            <TextBlock Text="Martingale" FontSize="13" FontWeight="SemiBold"
                       Foreground="#FFB8B8B8" Margin="0,0,0,4"/>

            <TextBlock Text="Factor" Style="{StaticResource LabelStyle}"/>
            <TextBox x:Name="FactorTextBox"
                     Text="{Binding FactorText, UpdateSourceTrigger=PropertyChanged}"
                     Style="{StaticResource InputStyle}"
                     Width="65"
                     HorizontalAlignment="Left"/>

            <TextBlock Text="Level" Style="{StaticResource LabelStyle}"/>
            <TextBox Text="{Binding LevelText, UpdateSourceTrigger=PropertyChanged}"
                     Style="{StaticResource InputStyle}"
                     Width="65"
                     HorizontalAlignment="Left"/>

            <!-- Toggle Switch -->
            <StackPanel Orientation="Horizontal" Margin="0,22,0,0" VerticalAlignment="Center">
                <TextBlock Text="Ativar" Foreground="#FFCCCCCC" FontSize="11" VerticalAlignment="Center" Margin="0,0,6,0"/>
                <ToggleButton IsChecked="{Binding MartingaleEnabled}" Style="{StaticResource ToggleSwitchStyle}"/>
            </StackPanel>
            
            <!-- Save Configuration Checkbox -->
            <Grid Margin="0,12,0,0">
                <CheckBox HorizontalAlignment="Right"
                          VerticalAlignment="Bottom"
                          Content="Save"
                          FontSize="11"
                          Foreground="#FFCCCCCC"
                          IsChecked="{Binding SaveConfiguration}">
                    <CheckBox.Style>
                        <Style TargetType="CheckBox">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="CheckBox">
                                        <StackPanel Orientation="Horizontal">
                                            <Border x:Name="CheckBorder"
                                                    Width="16" Height="16"
                                                    Background="#FF3F3F46"
                                                    BorderBrush="#FF404040"
                                                    BorderThickness="1"
                                                    CornerRadius="3"
                                                    Margin="0,0,6,0">
                                                <Path x:Name="CheckMark"
                                                      Data="M 2 6 L 6 10 L 14 2"
                                                      Stroke="#FFFFC107"
                                                      StrokeThickness="2"
                                                      Visibility="Collapsed"/>
                                            </Border>
                                            <ContentPresenter Content="{TemplateBinding Content}"
                                                              VerticalAlignment="Center"/>
                                        </StackPanel>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsChecked" Value="True">
                                                <Setter TargetName="CheckMark" Property="Visibility" Value="Visible"/>
                                                <Setter TargetName="CheckBorder" Property="BorderBrush" Value="#FFFFC107"/>
                                            </Trigger>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter TargetName="CheckBorder" Property="Background" Value="#FF4A4A4A"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </CheckBox.Style>
                </CheckBox>
            </Grid>
        </StackPanel>
    </ScrollViewer>
</UserControl> 