using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Threading;

namespace Excalibur.ViewModels;

public class AsyncCommand : ICommand
{
    private readonly Func<Task> _execute;
    private readonly Func<bool>? _canExecute;
    private volatile bool _isExecuting;
    private readonly object _lock = new object();

    public AsyncCommand(Func<Task> execute, Func<bool>? canExecute = null)
    {
        _execute = execute ?? throw new ArgumentNullException(nameof(execute));
        _canExecute = canExecute;
    }

    public event EventHandler? CanExecuteChanged
    {
        add { CommandManager.RequerySuggested += value; }
        remove { CommandManager.RequerySuggested -= value; }
    }

    public bool CanExecute(object? parameter)
    {
        try
        {
            return !_isExecuting && (_canExecute?.Invoke() ?? true);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[AsyncCommand] Error in CanExecute: {ex.Message}");
            return false;
        }
    }

    public async void Execute(object? parameter)
    {
        if (!CanExecute(parameter)) return;

        lock (_lock)
        {
            if (_isExecuting) return;
            _isExecuting = true;
        }

        OnCanExecuteChanged();

        try
        {
            await _execute();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[AsyncCommand] Unhandled exception: {ex}");
            
            // Instead of showing MessageBox directly, use dispatcher to ensure UI thread
            var dispatcher = Application.Current?.Dispatcher;
            if (dispatcher != null)
            {
                dispatcher.Invoke(() =>
                {
                    try
                    {
                        MessageBox.Show($"An error occurred: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                    catch
                    {
                        // Fallback if MessageBox fails
                        System.Diagnostics.Debug.WriteLine($"[AsyncCommand] Failed to show error dialog: {ex.Message}");
                    }
                });
            }
        }
        finally
        {
            lock (_lock)
            {
                _isExecuting = false;
            }
            OnCanExecuteChanged();
        }
    }

    protected virtual void OnCanExecuteChanged()
    {
        CommandManager.InvalidateRequerySuggested();
    }
}